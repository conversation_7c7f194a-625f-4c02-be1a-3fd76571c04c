package top.kx.kxss.oauth.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.BizException;
import top.kx.kxss.base.entity.user.BaseOrg;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.kxss.model.vo.result.UserQuery;
import top.kx.kxss.oauth.biz.OauthUserBiz;
import top.kx.kxss.oauth.enumeration.GrantType;
import top.kx.kxss.oauth.granter.TokenGranterBuilder;
import top.kx.kxss.oauth.service.CaptchaService;
import top.kx.kxss.oauth.service.UserInfoService;
import top.kx.kxss.oauth.vo.result.DefUserInfoResultVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;
import top.kx.kxss.oauth.vo.result.OrgResultVO;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.update.tenant.*;
import top.kx.kxss.userinfo.biz.EmployeeHelperBiz;

import java.util.List;

/**
 * 认证Controller
 *
 * <AUTHOR>
 * @date 2020年03月31日10:10:36
 */
@Slf4j
@RestController
@RequestMapping("/anyone")
@AllArgsConstructor
@Api(value = "用户基本信息", tags = "用户基本信息")
public class UserInfoController {

    private final OauthUserBiz oauthUserBiz;
    private final TokenGranterBuilder tokenGranterBuilder;
    private final UserInfoService userInfoService;
    private final DefUserService defUserService;
    private final CaptchaService captchaService;
    private final EmployeeHelperBiz employeeHelperBiz;

    /**
     * 单体查询用户
     *
     * @param query 参数
     * @return 查询结果
     */
    @ApiOperation(value = "查询用户详细", notes = "查询用户详细：@LoginUser 注解专用")
    @PostMapping(value = "/getSysUserById")
    @ApiIgnore
    public R<SysUser> getSysUserById(@RequestBody UserQuery query) {
        return R.success(employeeHelperBiz.getSysUserById(query));
    }

    /**
     * 获取当前登录的用户信息
     */
    @ApiOperation(value = "获取当前登录的用户信息", notes = "获取当前登录的用户信息：登录后，查询用户信息")
    @GetMapping(value = "/getUserInfoById")
    public R<DefUserInfoResultVO> getUserInfoById(@RequestParam(required = false) Long userId) throws BizException {
        if (userId == null) {
            userId = ContextUtil.getUserId();
        }
        return R.success(oauthUserBiz.getUserById(userId));
    }

    @ApiOperation(value = "切换单位和部门")
    @PutMapping("/switchTenantAndOrg")
    public R<LoginResultVO> switchOrg(@RequestParam Long companyId, @RequestParam Long deptId) {
        return R.success(tokenGranterBuilder.getGranter(GrantType.PASSWORD).switchOrg(companyId, deptId));
    }


    /**
     * 修改头像
     *
     * @param data 用户头像信息
     * @return 用户
     */
    @ApiOperation(value = "修改头像", notes = "修改头像")
    @PutMapping("/avatar")
    @WebLog("'修改头像:' + #data.id")
    public R<Boolean> avatar(@RequestBody @Validated DefUserAvatarUpdateVO data) {
        return R.success(defUserService.updateAvatar(data));
    }

    /**
     * 修改密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "修改密码", notes = "修改密码")
    @PutMapping("/password")
    @WebLog("'修改密码:' + #data.id")
    public R<Boolean> updatePassword(@RequestBody @Validated DefUserPasswordUpdateVO data) {
        return R.success(defUserService.updatePassword(data));
    }

    /**
     * 修改手机
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "修改手机", notes = "修改手机")
    @PutMapping("/mobile")
    @WebLog("'修改手机:' + #data.mobile")
    public R<Boolean> updateMobile(@RequestBody @Validated DefUserMobileUpdateVO data) {
        R<Boolean> r = captchaService.checkCaptcha(data.getMobile(), data.getTemplateCode(), data.getCode());
        if (!r.getIsSuccess()) {
            return r;
        }
        return R.success(defUserService.updateMobile(data));
    }

    /**
     * 修改邮箱
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "修改邮箱", notes = "修改邮箱")
    @PutMapping("/email")
    @WebLog("'修改邮箱:' + #data.email")
    public R<Boolean> updateEmail(@RequestBody @Validated DefUserEmailUpdateVO data) {
        R<Boolean> r = captchaService.checkCaptcha(data.getEmail(), data.getTemplateCode(), data.getCode());
        if (!r.getIsSuccess()) {
            return r;
        }
        return R.success(defUserService.updateEmail(data));
    }

    /**
     * 修改个人信息
     *
     * @param data 用户基础信息
     * @return 用户
     */
    @ApiOperation(value = "修改基础信息")
    @PutMapping("/baseInfo")
    @WebLog(value = "'修改基础信息:' + #data?.id", request = false)
    public R<Boolean> updateBaseInfo(@RequestBody @Validated DefUserBaseInfoUpdateVO data) {
        return R.success(defUserService.updateBaseInfo(data));
    }


    @ApiOperation(value = "查询单位和部门")
    @GetMapping("/findCompanyDept")
    @WebLog(value = "根据租户ID查询单位和部门")
    public R<OrgResultVO> findCompanyDept() {
        return R.success(userInfoService.findCompanyAndDept());
    }

    @ApiOperation(value = "根据单位查询部门")
    @WebLog(value = "根据单位查询部门")
    @GetMapping("/findDeptByCompany")
    public R<List<BaseOrg>> findDeptByCompany(@RequestParam Long companyId) {
        return R.success(userInfoService.findDeptByCompany(companyId));
    }


}
