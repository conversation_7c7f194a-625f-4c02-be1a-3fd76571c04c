package top.kx.kxss.oauth.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.response.IgnoreResponseBodyAdvice;
import top.kx.basic.base.R;
import top.kx.basic.exception.BizException;
import top.kx.kxss.oauth.granter.CaptchaTokenGranter;
import top.kx.kxss.oauth.service.CaptchaService;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_STRING;
import static top.kx.kxss.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;

/**
 * 验证码服务
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/9/29 5:37 PM
 * @create [2022/9/29 5:37 PM ] [tangyh] [初始创建]
 */
@Slf4j
@RestController
@RequestMapping("/anyTenant")
@AllArgsConstructor
@Api(value = "如:短信/邮箱/图片", tags = "验证码")
public class CaptchaController {

    private final CaptchaService captchaService;

    /**
     * 验证验证码
     *
     * @param key  验证码唯一uuid key
     * @param code 验证码
     */
    @ApiOperation(value = "验证验证码是否正确", notes = "验证验证码")
    @GetMapping(value = "/checkCaptcha")
    public R<Boolean> checkCaptcha(@RequestParam(value = "key") String key, @RequestParam(value = "code") String code,
                                   @RequestParam(value = "templateCode", required = false, defaultValue = CaptchaTokenGranter.GRANT_TYPE)
                                   String templateCode) throws BizException {
        return this.captchaService.checkCaptcha(key, templateCode, code);
    }

    @ApiOperation(value = "获取图片验证码", notes = "获取图片验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "key", value = "唯一字符串: 前端随机生成一个唯一字符串用于生成验证码，并将key传给后台用于验证", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @GetMapping(value = "/captcha", produces = "image/png")
    @IgnoreResponseBodyAdvice
    public void captcha(@RequestParam(value = "key") String key, HttpServletResponse response) throws IOException {
        this.captchaService.createImg(key, response);
    }

    @ApiOperation(value = "发送短信验证码", notes = "发送短信验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobile", value = "手机号", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "templateCode", value = "模板编码: 在「运营平台」-「消息模板」-「模板标识」配置一个短信模板", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @GetMapping(value = "/sendSmsCode")
    public R<Boolean> sendSmsCode(@RequestParam(value = "mobile") String mobile,
                                  @RequestParam(value = "templateCode") String templateCode) {
        return captchaService.sendSmsCode(mobile, templateCode);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobile", value = "邮箱", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "templateCode", value = "模板编码: 在「运营平台」-「消息模板」-「模板标识」配置一个邮件模板", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "发送邮箱验证码", notes = "发送邮箱验证码")
    @GetMapping(value = "/sendEmailCode")
    public R<Boolean> sendEmailCode(@RequestParam(value = "email") String email,
                                    @RequestParam(value = "templateCode") String templateCode) {
        return captchaService.sendEmailCode(email, templateCode);
    }

}
