package top.kx.kxss.base.controller.lottery;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.lottery.LotteryActivityBatchService;
import top.kx.kxss.base.entity.lottery.LotteryActivityBatch;
import top.kx.kxss.base.vo.save.lottery.LotteryActivityBatchSaveVO;
import top.kx.kxss.base.vo.update.lottery.LotteryActivityBatchUpdateVO;
import top.kx.kxss.base.vo.result.lottery.LotteryActivityBatchResultVO;
import top.kx.kxss.base.vo.query.lottery.LotteryActivityBatchPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 活动与防伪码批次关联表
 * </p>
 *
 * <AUTHOR>
 * @date 2025-09-15 17:23:11
 * @create [2025-09-15 17:23:11] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/lotteryActivityBatch")
@Api(value = "LotteryActivityBatch", tags = "活动与防伪码批次关联表")
public class LotteryActivityBatchController extends SuperController<LotteryActivityBatchService, Long, LotteryActivityBatch, LotteryActivityBatchSaveVO,
    LotteryActivityBatchUpdateVO, LotteryActivityBatchPageQuery, LotteryActivityBatchResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


