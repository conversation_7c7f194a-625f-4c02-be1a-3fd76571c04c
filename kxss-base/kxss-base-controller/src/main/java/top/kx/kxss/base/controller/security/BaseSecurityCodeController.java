package top.kx.kxss.base.controller.security;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.BaseSecurityCode;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.purchase.BasePurchaseDetails;
import top.kx.kxss.base.service.BaseSecurityCodeService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.purchase.BasePurchaseDetailsService;
import top.kx.kxss.base.vo.query.BaseSecurityCodePageQuery;
import top.kx.kxss.base.vo.query.BaseSecurityCodeRegisterPageQuery;
import top.kx.kxss.base.vo.result.BaseSecurityCodeRegisterResultVO;
import top.kx.kxss.base.vo.result.BaseSecurityCodeResultVO;
import top.kx.kxss.base.vo.result.SecurityCodeFileVO;
import top.kx.kxss.base.vo.result.purchase.BasePurchaseDetailsResultVO;
import top.kx.kxss.base.vo.save.BaseSecurityCodeSaveVO;
import top.kx.kxss.base.vo.update.BaseSecurityCodeUpdateVO;
import top.kx.kxss.model.enumeration.base.CategoryEnum;
import top.kx.kxss.model.enumeration.base.ProductCategoryEnum;
import top.kx.kxss.model.enumeration.base.SecurityCodeStatusEnum;
import top.kx.kxss.system.entity.system.DefDict;
import top.kx.kxss.system.service.system.DefDictService;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseSecurityCode")
@Api(value = "BaseSecurityCode", tags = "防伪信息")
public class BaseSecurityCodeController extends SuperController<BaseSecurityCodeService, Long, BaseSecurityCode, BaseSecurityCodeSaveVO,
        BaseSecurityCodeUpdateVO, BaseSecurityCodePageQuery, BaseSecurityCodeResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Resource
    private BaseProductService productService;
    @Resource
    private DefDictService defDictService;
    @Resource
    private BasePurchaseDetailsService  basePurchaseDetailsService;

    @Override
    public R<BaseSecurityCode> handlerSave(BaseSecurityCodeSaveVO model) {
        model.setSelectNum(0);
        if (ObjectUtil.equal(model.getStatus(), SecurityCodeStatusEnum.BIND.getCode())) {
            if (ObjectUtil.isNull(model.getBillDate()) || ObjectUtil.isNull(model.getProductId())
                    || ObjectUtil.isNull(model.getBindUser())
                    || StrUtil.isAllBlank(model.getSupplier(), model.getWareHouse())) {
                throw new BizException("请填写绑定状态相关参数【绑定/报废日期、绑定/报废人员、商品、仓库、供应商】");
            }
        }
        if (ObjectUtil.equal(model.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode())) {
            if (ObjectUtil.isNull(model.getRegisterTime()) || ObjectUtil.isNull(model.getMemberId())
                    || ObjectUtil.isNull(model.getBillDate())
                    || ObjectUtil.isNull(model.getProductId())
                    || ObjectUtil.isNull(model.getBindUser())
                    || StrUtil.isAllBlank(model.getSupplier(), model.getWareHouse())) {
                throw new BizException("请填写注册状态相关参数【绑定/报废日期、绑定/报废人员、注册日期、商品、会员、仓库、供应商】");
            }

        }
        if (ObjectUtil.equal(model.getStatus(), SecurityCodeStatusEnum.DEL.getCode())) {
            if (ObjectUtil.isNull(model.getBillDate()) || ObjectUtil.isNull(model.getProductId())
                    || ObjectUtil.isNull(model.getBindUser())) {
                throw new BizException("请填写报废状态相关参数【绑定/报废日期、绑定/报废人员、商品】");
            }
            model.setScrapTime(model.getBillDate());
            model.setScrapUser(model.getBindUser());
            model.setBindUser(null);
            model.setBillDate(null);
        }
        if (ObjectUtil.isNotNull(model.getDistributorId())) {
            model.setPurchaseControlBy(ContextUtil.getEmployeeId());
            model.setPurchaseEnterType("3");
        }
        if (superService.checkCode(model.getCode(), null)) {
            throw new BizException("序列号已存在");
        }
        if (superService.checkSecurityCode(model.getSecurityCode(), null)) {
            throw new BizException("防伪码已存在");
        }
        if (StrUtil.isBlank(model.getBigCode())) {
            model.setBigCode("-");
        }
        return super.handlerSave(model);
    }

    @Override
    public QueryWrap<BaseSecurityCode> handlerWrapper(BaseSecurityCode model, PageParams<BaseSecurityCodePageQuery> params) {
        QueryWrap<BaseSecurityCode> baseSecurityCodeQueryWrap = super.handlerWrapper(model, params);
        BaseSecurityCodePageQuery query = params.getModel();
        if (StringUtils.isNotBlank(query.getCategory())) {
            CategoryEnum categoryEnum = CategoryEnum.get(query.getCategory());
            if (ObjectUtil.isNotNull(categoryEnum)) {
                String insql = null;
                switch (categoryEnum) {
                    case QZ:
                        insql = "select id from base_product where delete_flag = 0 and category = '" + ProductCategoryEnum.QZ.getCode() + "'";
                        break;
                    case QG:
                        insql = "select id from base_product where delete_flag = 0 and category = '" + ProductCategoryEnum.QG.getCode() + "'";
                        break;
                    case HC:
                        List<DefDict> product = defDictService.list(Wraps.<DefDict>lbQ().eq(DefDict::getParentKey, "PRODUCT")
                                .notIn(DefDict::getKey, Arrays.asList(ProductCategoryEnum.QZ.getCode(), ProductCategoryEnum.QG.getCode())));
                        if (CollUtil.isNotEmpty(product)) {
                            List<String> keys = product.stream().map(DefDict::getKey).collect(Collectors.toList());
                            insql = "select id from base_product where delete_flag = 0 and category in ( " +  String.join(",",
                                    keys) + ")";
                        }
                        break;
                    default:
                        break;
                }
                if (StringUtils.isNotBlank(insql)) {
                    if (StringUtils.isNotBlank(query.getBrand())) {
                        insql += " and brand = '" + query.getBrand() + "'";
                    }
                    baseSecurityCodeQueryWrap.lambda().inSql(BaseSecurityCode::getProductId, insql);
                }
            }
        }
        if (Objects.nonNull(query.getQueryAttribute()) && query.getQueryAttribute()) {
            baseSecurityCodeQueryWrap.lambda().inSql(BaseSecurityCode::getId, "select distinct security_code_id from base_security_code_attribute where delete_flag = 0");
        }
        return baseSecurityCodeQueryWrap;
    }

    @Override
    public R<BaseSecurityCode> handlerUpdate(BaseSecurityCodeUpdateVO model) {
        if (ObjectUtil.equal(model.getStatus(), SecurityCodeStatusEnum.BIND.getCode())) {
            if (ObjectUtil.isNull(model.getBillDate()) || ObjectUtil.isNull(model.getProductId())
                    || ObjectUtil.isNull(model.getBindUser())
                    || StrUtil.isAllBlank(model.getSupplier(), model.getWareHouse())) {
                throw new BizException("请填写绑定状态相关参数【绑定/报废日期、绑定/报废人员、商品、仓库、供应商】");
            }
        }
        if (ObjectUtil.equal(model.getStatus(), SecurityCodeStatusEnum.REGISTER.getCode())) {
            if (ObjectUtil.isNull(model.getRegisterTime()) || ObjectUtil.isNull(model.getMemberId())
                    || ObjectUtil.isNull(model.getBillDate())
                    || ObjectUtil.isNull(model.getProductId())
                    || ObjectUtil.isNull(model.getBindUser())
                    || StrUtil.isAllBlank(model.getSupplier(), model.getWareHouse())) {
                throw new BizException("请填写注册状态相关参数【绑定/报废日期、绑定/报废人员、注册日期、商品、会员、仓库、供应商】");
            }

        }
        if (ObjectUtil.equal(model.getStatus(), SecurityCodeStatusEnum.DEL.getCode())) {
            if (ObjectUtil.isNull(model.getBillDate()) || ObjectUtil.isNull(model.getProductId())
                    || ObjectUtil.isNull(model.getBindUser())) {
                throw new BizException("请填写报废状态相关参数【绑定/报废日期、绑定/报废人员、商品】");
            }
            model.setScrapTime(model.getBillDate());
            model.setScrapUser(model.getBindUser());
            model.setBindUser(null);
            model.setBillDate(null);
        }
        BaseSecurityCode securityCode = superService.getById(model.getId());
        if (ObjectUtil.isNotNull(model.getDistributorId())
                && !ObjectUtil.equal(model.getDistributorId(), securityCode.getDistributorId())) {
            model.setPurchaseControlBy(ContextUtil.getEmployeeId());
            model.setPurchaseEnterType("3");
        }
        if (superService.checkCode(model.getCode(), model.getId())) {
            throw new BizException("序列号已存在");
        }
        if (superService.checkSecurityCode(model.getSecurityCode(), model.getId())) {
            throw new BizException("防伪码已存在");
        }
        if (StrUtil.isBlank(model.getBigCode())) {
            model.setBigCode("-");
        }
        return super.handlerUpdate(model);
    }

    @Override
    public void handlerResult(IPage<BaseSecurityCodeResultVO> page) {
        Map<String, List<BasePurchaseDetailsResultVO>> purchaseDetailsMap = new HashMap<>();
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<BasePurchaseDetails> purchaseDetailsList = basePurchaseDetailsService.list(Wraps.<BasePurchaseDetails>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                    .in(BasePurchaseDetails::getSecurityCode, page.getRecords().stream().map(BaseSecurityCodeResultVO::getSecurityCode).distinct().collect(Collectors.toList())).orderByAsc(SuperEntity::getCreatedTime));
            List<BasePurchaseDetailsResultVO> purchaseDetailsResultVOList = BeanPlusUtil.toBeanList(purchaseDetailsList, BasePurchaseDetailsResultVO.class);
            echoService.action(purchaseDetailsResultVOList);
            purchaseDetailsMap = CollectionUtil.isNotEmpty(purchaseDetailsResultVOList) ? purchaseDetailsResultVOList.stream().collect(Collectors.groupingBy(BasePurchaseDetailsResultVO::getSecurityCode)) : new HashMap<>();
            for (BaseSecurityCodeResultVO record : page.getRecords()) {
                if (ObjectUtil.equal(record.getStatus(), SecurityCodeStatusEnum.DEL.getCode())) {
                    record.setBillDate(record.getScrapTime());
                    record.setBindUser(record.getScrapUser());
                }
                if (purchaseDetailsMap.containsKey(record.getSecurityCode())) {
                    List<BasePurchaseDetailsResultVO> purchaseDetailsResultVOS= purchaseDetailsMap.get(record.getSecurityCode());
                    if (CollUtil.isNotEmpty(purchaseDetailsResultVOS)) {
                        record.setFirstLevelPurchaseVO(purchaseDetailsResultVOS.get(0));
                        if (CollUtil.size(purchaseDetailsResultVOS) > 1) {
                            record.setSecondLevelPurchaseVO(purchaseDetailsResultVOS.get(1));
                        }
                    }
                }
            }
        }
        super.handlerResult(page);
    }

    /**
     * 导入流程模型
     *
     * @param file 上传文件
     */
    @PostMapping(value = "/import")
    @ApiOperation("导入")
    @WebLog("导入")
    public R<SecurityCodeFileVO> importCode(@RequestParam(value = "file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return fail("请上传文件!");
        }
        if (!file.getOriginalFilename().endsWith(".txt")) {
            return fail("仅支持txt文件!");
        }
        return success(superService.importCode(file, "CODE"));
    }

    /**
     * 导入流程模型
     *
     * @param file 上传文件
     */
    @PostMapping(value = "/importBigSmallCode")
    @ApiOperation("导入")
    @WebLog("导入")
    public R<SecurityCodeFileVO> importBigSmallCode(@RequestParam(value = "file") MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return fail("请上传文件!");
        }
        if (!file.getOriginalFilename().endsWith(".txt")) {
            return fail("仅支持txt文件!");
        }
        return success(superService.importCode(file, "BIG_SMALL_CODE"));
    }

    @ApiOperation(value = "分页查询会员商品信息", notes = "分页查询商品信息")
    @PostMapping("/pageByMemberId")
    @WebLog(value = "'分页查询商品信息:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<BaseSecurityCodeRegisterResultVO>> pageByMemberId(@RequestBody @Validated PageParams<BaseSecurityCodeRegisterPageQuery> params) {
        IPage<BaseSecurityCode> page = params.buildPage(BaseSecurityCode.class);
        BaseSecurityCodeRegisterPageQuery query = params.getModel();
        BaseSecurityCode model = BeanUtil.toBean(query, BaseSecurityCode.class);
        LbQueryWrap<BaseSecurityCode> wraps = Wraps.lbq(model, params.getExtra(), BaseSecurityCode.class);
        wraps.orderByDesc(BaseSecurityCode::getRegisterTime);
        superService.page(page, wraps);
        IPage<BaseSecurityCodeRegisterResultVO> pageVO = BeanPlusUtil.toBeanPage(page, BaseSecurityCodeRegisterResultVO.class);

        List<BaseSecurityCodeRegisterResultVO> records = pageVO.getRecords();
        //图片信息
        List<Long> productIds = records.stream().map(BaseSecurityCodeRegisterResultVO::getProductId).collect(Collectors.toList());
        Map<Long, BaseProduct> productMap = CollectionUtil.isNotEmpty(productIds) ? productService.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, k -> k)) : new HashMap<>();
        for (BaseSecurityCodeRegisterResultVO record : records) {
            if (ObjectUtil.isNotNull(record.getProductId())) {
                BaseProduct baseProduct = productMap.get(record.getProductId());
                if (ObjectUtil.isNotNull(baseProduct)) {
                    BeanUtil.copyProperties(baseProduct, record);
                }
            }
        }
        echoService.action(pageVO);
        return R.success(pageVO);
    }



//    @Deprecated
//    @ApiOperation("添加属性")
//    @PostMapping(value = "/addAttribute")
//    @WebLog("添加属性")
//    public R<Boolean> addAttribute(@RequestBody @Validated SecurityCodeAttributeUpdateVO attributeUpdateVO) {
//        return success(superService.addAttribute(attributeUpdateVO));
//    }

//    @Deprecated
//    @ApiOperation(value = "删除规格属性")
//    @DeleteMapping("/removeAttribute")
//    @WebLog("删除规格属性")
//    public R<Boolean> removeAttribute(@RequestParam String code) {
//        return success(superService.removeAttribute(code));
//    }



    @ApiOperation(value = "检测防伪码是否存在")
    @GetMapping("/checkSecurityCode")
    @WebLog("检测防伪码是否存在")
    public R<Boolean> checkSecurityCode(@RequestParam String code, @RequestParam(required = false) Long id) {
        return success(superService.checkSecurityCode(code, id));
    }

    @ApiOperation(value = "检测防伪序列号是否存在")
    @GetMapping("/checkCode")
    @WebLog("检测防伪码是否存在")
    public R<Boolean> checkCode(@RequestParam String code, @RequestParam(required = false) Long id) {
        return success(superService.checkCode(code, id));
    }

    /**
     * 校验防伪码是否绑定商品， 防伪码不在系统中，也认为未绑定商品
     * @param securityCode
     * @return
     */
    @ApiOperation(value = "检测防伪序列号是否存在绑定商品")
    @GetMapping("/checkCodeBindProduct")
    @WebLog("检测防伪序列号是否存在绑定商品")
    public R<Boolean> checkCodeBindProduct(@RequestParam String securityCode) {
        ArgumentAssert.isTrue(StringUtils.isNotBlank(securityCode), "参数不能为空");
        return success(superService.checkCodeBindProduct(securityCode));
    }

//    @ApiOperation(value = "查询最新一条包含规格属性的数据")
//    @GetMapping("/queryOneContainAttribute")
//    @WebLog("查询最新一条包含属性的数据")
//    public R<BaseSecurityCodeResultVO> queryOneContainAttribute(@RequestParam(required = false) String productCategory,
//                                                                @RequestParam(required = false) String securityCode) {
//        ArgumentAssert.isTrue(StringUtils.isNotBlank(productCategory) || StringUtils.isNotBlank(securityCode), "参数不能为空");
//        return success(superService.queryOneContainAttribute(productCategory, securityCode));
//    }


    /**
     * 属性设置钱
     * @param securityCode
     * @return
     */
    @ApiOperation(value = "根据券码查询基础属性信息")
    @GetMapping("/getBaseAttributeBySecurityCode")
    @WebLog("根据券码查询基础属性信息")
    public R<BaseSecurityCodeResultVO> getBaseAttributeBySecurityCode(@RequestParam String securityCode) {
        return R.success(superService.getBaseAttributeBySecurityCode(securityCode));
    }


    @ApiOperation(value = "根据券码查询信息")
    @GetMapping("/getOneBySecurityCode")
    @WebLog("根据券码查询信息")
    public R<BaseSecurityCodeResultVO> getOneBySecurityCode(@RequestParam String securityCode) {
        return R.success(superService.getOneBySecurityCode(securityCode));
    }



    @Override
    public R<BaseSecurityCodeResultVO> getDetail(Long aLong) {
        return R.success(superService.getDetail(aLong));
    }

    //    public static void main(String[] args) {
//        long a = 473992527931376329L;
//        long b = 987878889654201L;
//
//        System.out.println(IdUtil.getSnowflake().nextIdStr());
//
//        for (int i = 0; i < 10; i++) {
//            System.out.println(IdUtil.getSnowflake().nextIdStr());
////            System.out.println("a：" + (a + i) + "    b：" + (b + i)
////                    + "    code：" + SecureUtil.sha256((a + i)+ "" + (b + i)));
//        }
//
//    }

    /**
     * java to excel 导入appache的poi 3.15 jar 包
     */

    // 产生要储存的集合
    protected static List<String> getUsers(int j) {
        List<String> users = new ArrayList<>();
        for (int i = 0; i < 20000; i++) {
            String str = IdUtil.getSnowflake().nextIdStr();
            int random6 = (int) ((Math.random() * 9 + 1) * 100000);
            String substring = str.substring(str.length() - 10, str.length());
            substring = substring.concat(random6 + "");
            substring = substring.substring(substring.length() - 12, substring.length());
            String pre = "051425".concat(j + "");
            if (!users.contains(pre.concat(substring))) {
                users.add(pre.concat(substring));
            }
        }
        return users;
    }

    protected static List<String> getCode(int j) {
        List<String> users = new ArrayList<>();
        for (int i = 0; i < 20000; i++) {
            users.add(getCodeStr(users, j + 1));
        }
        return users;
    }

    protected static String getCodeStr(List<String> users, int j) {
        int digit = 6;
        int num = (int) ((Math.random() * 9 + 1) * 100000);
        String format = String.format("%0" + digit + "d", num);
        String concat = "250514".concat(j + "").concat(format);
        if (users.contains(concat)) {
            return getCodeStr(users, j);
        }
        return concat;
    }

    public static void main(String[] args) {
        for (int j = 0; j < 1; j++) {
            try {
                String filePath = "/Users/<USER>/Desktop/" + j + "_051425.txt";
                FileWriter fw = null;
                File file = new File(filePath);
                if (!file.exists()) {
                    file.createNewFile();
                }
                fw = new FileWriter(filePath);
                BufferedWriter bw = new BufferedWriter(fw);
                //第一步，创建一个workbook对应一个excel文件
                HSSFWorkbook workbook = new HSSFWorkbook();
                //第二部，在workbook中创建一个sheet对应excel中的sheet
                HSSFSheet sheet = workbook.createSheet("sheet1");
                //第三部，在sheet表中添加表头第0行，老版本的poi对sheet的行列有限制
                HSSFRow row = sheet.createRow(0);
                //第四步，创建单元格，设置表头
                HSSFCell cell = row.createCell(0);
                cell.setCellValue("序列号");
                HSSFCell cell1 = row.createCell(1);
                cell1.setCellValue("防伪号");
                HSSFCell cell2 = row.createCell(2);
                cell2.setCellValue("链接");

                //第五步，写入实体数据，实际应用中这些数据从数据库得到,对象封装数据，集合包对象。对象的属性值对应表的每行的值
                List<String> users = getUsers(j + 1);
                List<String> code = getCode(j);
                System.out.println("数据：" + JSON.toJSONString(users));
                for (int i = 0; i < users.size(); i++) {
                    HSSFRow row1 = sheet.createRow(i + 1);
                    String user = users.get(i);
                    //创建单元格设值
                    row1.createCell(0).setCellValue(code.get(i));
                    row1.createCell(1).setCellValue(user);
                    row1.createCell(2).setCellValue("https://fw.kxss147.com/fw/?q=" + user);
                    bw.write(code.get(i).concat(",").concat(user).concat(",")
                            .concat("https://fw.kxss147.com/fw/?q=" + user).concat("\n"));
                }

                //将文件保存到指定的位置
                bw.close();
                FileOutputStream fos = new FileOutputStream("/Users/<USER>/Desktop/" + (j + 1) + "_051425.xls");
                workbook.write(fos);
                System.out.println("写入成功");
                fos.close();

            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

}


