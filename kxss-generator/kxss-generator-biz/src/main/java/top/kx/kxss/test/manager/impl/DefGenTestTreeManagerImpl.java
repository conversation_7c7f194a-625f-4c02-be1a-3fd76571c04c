package top.kx.kxss.test.manager.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.test.manager.DefGenTestTreeManager;
import top.kx.kxss.test.mapper.DefGenTestTreeMapper;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.test.entity.DefGenTestTree;

/**
 * <p>
 * 通用业务实现类
 * 测试树结构
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-20 00:28:30
 * @create [2022-04-20 00:28:30] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefGenTestTreeManagerImpl extends SuperManagerImpl<DefGenTestTreeMapper, DefGenTestTree> implements DefGenTestTreeManager {

}


