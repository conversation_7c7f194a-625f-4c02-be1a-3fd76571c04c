package top.kx.kxss.system.controller.tenant;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.system.entity.tenant.DefDatasourceConfig;
import top.kx.kxss.system.service.tenant.DefDatasourceConfigService;
import top.kx.kxss.system.vo.query.tenant.DefDatasourceConfigPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefDatasourceConfigResultVO;
import top.kx.kxss.system.vo.save.tenant.DefDatasourceConfigSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefDatasourceConfigUpdateVO;


/**
 * <p>
 * 前端控制器
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-13
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defDatasourceConfig")
@Api(value = "DefDatasourceConfig", tags = "数据源")
public class DefDatasourceConfigController extends SuperController<DefDatasourceConfigService, Long, DefDatasourceConfig, DefDatasourceConfigSaveVO, DefDatasourceConfigUpdateVO, DefDatasourceConfigPageQuery, DefDatasourceConfigResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @ApiOperation(value = "测试数据库链接")
    @PostMapping("/testConnect")
    public R<Boolean> testConnect(@RequestParam Long id) {
        return R.success(superService.testConnection(id));
    }
}
