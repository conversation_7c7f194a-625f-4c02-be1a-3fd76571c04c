<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>top.kx.kxss</groupId>
        <artifactId>kxss-activiti</artifactId>
        <version>4.13.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>top.kx.kxss</groupId>
    <artifactId>kxss-activiti-biz</artifactId>
    <description>工作流业务模块</description>

    <dependencies>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-activiti-entity</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>

        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-databases</artifactId>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-echo-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-boot</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-json-converter</artifactId>
        </dependency>

        <!-- batik start -->
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-anim -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-anim</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-awt-util -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-awt-util</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-bridge -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-bridge</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-codec -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-codec</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-css -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-css</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-dom -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-dom</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-ext -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-ext</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-gvt -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-gvt</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-js -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-js</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-parser -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-parser</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-script -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-script</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-svg-dom -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-svg-dom</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-svggen -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-svggen</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-transcoder -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-transcoder</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-util -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-util</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.xmlgraphics/batik-xml -->
        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>batik-xml</artifactId>
        </dependency>

    </dependencies>

</project>
