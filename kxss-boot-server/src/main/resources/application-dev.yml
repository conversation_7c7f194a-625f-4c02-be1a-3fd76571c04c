kxss:
  cache:
    type: REDIS
  redis:
    ip: 127.0.0.1
    port: 6379
    password: Aa123456*
    database: 2
  rabbitmq:
    enabled: false
    ip: *************
    port: 5672
    username: guest
    password: guest
  miniapp:
    configs:
      - appid: wx1f6d693773d23c75 #防伪小程序
        secret: 06ee5395fa7cfc2435f7e0f19cdc8bbf  #微信小程序的Secret
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
        clientId: p644ykiqjdi8vxf8srjqkr6e
        clientSecret: e5el9fp6wy0j05ujgnp83pkprw41sxbp
      - appid: wx08afa7313fe57482 #防伪员工小程序
        secret: 7320f098866b4269c251be2812116bda #微信小程序的Secret
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
        clientId: 1e03at7jpsju8g3o1wb9t9yj
        clientSecret: fvra778g4q8jpyh7c4k8kr7tir84c295
      - appid: wx64e5ddab83d4f254 #球桌小程序
        secret: ee32b92ddec39662b178dc956411001b #微信小程序的Secret
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
        clientId: c2qtx3an10pfj6ykxs6zgfw4
        clientSecret: n6rwlkuj4ctyb6du7kht9iko22p1u6s4
      - appid: wx9d1da3183b9ee252 #球杆小程序
        secret: 52acec928b1ceed2771132c025c379fc #微信小程序的Secret
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
        clientId: jxn97exvoujkzimdiarw3v1f
        clientSecret: h6y934mlcmbflt20hbs5oq4xo0imxmat
# 相当于 database.yml
lamp:
  mysql:
    filters: stat,wall
    db-type: mysql
    validation-query: SELECT 'x'
    username: 'root'
    password: 'Aa123456*'
    # 生产使用原生驱动，开发使用p6spy驱动打印日志
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************************************************************************************************************
  database: # 字段介绍参考 DatabaseProperties
    # 4.x 需要写死 column， 其他模式需要使用其他项目，而非改变此参数
    multiTenantType: NONE
    # 是否不允许写入数据  WriteInterceptor
    isNotWrite: false
    # 是否启用  sql性能规范插件
    isBlockAttack: false
    # 是否启用分布式事务
    isSeata: false
    # 生产环境请设置p6spy = false
    p6spy: true
    # 单页分页条数限制
    maxLimit: -1
    # 溢出总页数后是否进行处理
    overflow: false
    # 生成 countSql 优化掉 join, 现在只支持 left join
    optimizeJoin: true
    # id生成策略
    id-type: CACHE
    hutoolId:
      workerId: 0
      dataCenterId: 0
    cache-id:
      time-bits: 31
      worker-bits: 22
      seq-bits: 10
      epochStr: '2020-09-15'
      boost-power: 3
      padding-factor: 50
  file:
    storageType: QINIU_OSS  #  FAST_DFS LOCAL MIN_IO ALI_OSS HUAWEI_OSS QINIU_OSS
    delFile: true
    publicBucket:
      - public
    local:
      storagePath: /data/file/     # 文件存储路径  （ 某些版本的 window 需要改成  D:\\data\\projects\\uploadfile\\file\\  ）
      urlPrefix: https://www.hysz365.com:9666/file/   # 文件访问 （部署nginx后，配置nginx的ip，并配置nginx静态代理storage-path地址的静态资源）
      innerUrlPrefix: null  #  内网的url前缀
    fastDfs:
      urlPrefix: https://fastdfs.hysz365.com:/
    ali:
      # 请填写自己的阿里云存储配置
      urlPrefix: "https://zuihou-admin-cloud.oss-cn-beijing.aliyuncs.com/"
      bucket: "zuihou-admin-cloud"
      endpoint: "oss-cn-beijing.aliyuncs.com"
      accessKeyId: "填写你的id"
      accessKeySecret: "填写你的秘钥"
    minIo:
      endpoint: "https://static.tangyh.top/"
      accessKey: "lamp"
      secretKey: "ZHadmin123."
      bucket: "dev"
    huawei:
      # urlPrefix: "https://huawei.tangyh.top/"
      # endpoint: "obs.cn-southwest-2.myhuaweicloud.com"
      # accessKey: "1"
      # secretKey: "2"
      # location: "cn-southwest-2"
      # bucket: "pjcp-public"
      uriPrefix: https://pjcp-dev.obs.cn-southwest-2.myhuaweicloud.com/
      endpoint: "obs.cn-southwest-2.myhuaweicloud.com"
      accessKey: "CTPITSLOQDPMDKXZTXQU"
      secretKey: "7h2tpaueOPh9QVZqk5BVmQRUf1EDd0bmrGKVdU1C"
      bucket: "pjcp-dev"
      location: "cn-southwest-2"
    qiNiu:
      domain: file.hysz365.com
      useHttps: true
      zone: "z0"
      accessKey: "gTYgtkDhammyetMAzWzjBjH3QNRCkljzqm3SZQml"
      secretKey: "bigDdtHjCoOu7Sa54zdE1CbBiJTeJVpUBt0XNN2P"
      bucket: "kxss"
