package top.kx.kxss.oauth.controller;


import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;
import top.kx.basic.base.R;
import top.kx.basic.model.log.OptLogDTO;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.service.system.BaseOperationLogService;
import top.kx.kxss.base.vo.save.system.BaseOperationLogSaveVO;

/**
 * <p>
 * 前端控制器
 * 系统日志
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/optLog")
@Api(value = "OptLog", tags = "系统访问日志")
@AllArgsConstructor
@ApiIgnore
public class WebLogController {

    private final BaseOperationLogService baseOperationLogService;

    /**
     * 保存系统日志
     *
     * @param data 保存对象
     * @return 保存结果
     */
    @PostMapping
    @ApiOperation(value = "保存系统日志", notes = "保存系统日志不为空的字段")
    public R<Boolean> save(@RequestBody OptLogDTO data) {
        baseOperationLogService.save(BeanPlusUtil.toBean(data, BaseOperationLogSaveVO.class));
        return R.success(true);
    }

}
