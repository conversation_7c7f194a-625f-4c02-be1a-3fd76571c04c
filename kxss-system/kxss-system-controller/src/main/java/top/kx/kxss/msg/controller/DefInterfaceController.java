package top.kx.kxss.msg.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.msg.entity.DefInterface;
import top.kx.kxss.msg.service.DefInterfaceService;
import top.kx.kxss.msg.vo.query.DefInterfacePageQuery;
import top.kx.kxss.msg.vo.result.DefInterfaceResultVO;
import top.kx.kxss.msg.vo.save.DefInterfaceSaveVO;
import top.kx.kxss.msg.vo.update.DefInterfaceUpdateVO;

import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_STRING;
import static top.kx.kxss.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;

/**
 * <p>
 * 前端控制器
 * 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 16:45:45
 * @create [2022-07-04 16:45:45] [zuihou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/defInterface")
@Api(value = "DefInterface", tags = "接口")
public class DefInterfaceController extends SuperController<DefInterfaceService, Long, DefInterface, DefInterfaceSaveVO,
        DefInterfaceUpdateVO, DefInterfacePageQuery, DefInterfaceResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "code", value = "编码", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测资源编码是否可用", notes = "检测资源编码是否可用")
    @GetMapping("/check")
    public R<Boolean> check(@RequestParam(required = false) Long id, @RequestParam String code) {
        return success(superService.check(code, id));
    }
}


