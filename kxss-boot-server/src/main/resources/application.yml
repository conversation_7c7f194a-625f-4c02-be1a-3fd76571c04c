#环境配置
spring:
  profiles:
    active: prod
    group:
      # 覆盖顺序和这里的顺序无关，跟 --- 分割的顺序有关
      "dev": "dev,common,mysql,rabbitmq,redis"
      "prod": "prod,common,mysql,rabbitmq,redis"
      "test": "test,common,mysql,rabbitmq,redis"
---
#禁用nacos
spring:
  cloud:
    nacos:
      config:
        enabled: false
        refresh-enabled: false
      discovery:
        enabled: false
        instance-enabled: false

---
#端口信息
server:
  port: 16760
  # 优雅停机
  shutdown: GRACEFUL
  servlet:
    encoding:
      enabled: true
      charset: UTF-8
      force: true
  undertow:
    threads:
      io: 4 # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      worker: 80  # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
    buffer-size: 2048  # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理 , 每块buffer的空间大小,越小的空间被利用越充分
    direct-buffers: true  # 是否分配的直接内存

# 相当于 common.yml
lamp:
  grayscale:
    enabled: false
  webmvc:
    undertow: true
    header: false
  authentication:
    expire: 28800               # token有效期为8小时
    refreshExpire: 86400        # 刷新token有效期为24小时
  system:
    # 登录时否验证密码有效性 （常用于开发环境快速登录）
    verifyPassword: true
    # 登录时否验证验证码有效性 （常用于开发环境快速登录）
    verifyCaptcha: true
    # 默认用户密码
    defPwd: '123456'
    # 密码最大输错次数  小于0不限制
    maxPasswordErrorNum: 10
    # 密码错误锁定用户时间  除了0表示锁定到今天结束，还支持m、h、d、w、M、y等单位
    passwordErrorLockUserTime: '0'
    # log记录 top.kx.kxss.controller/service/biz包下 参数
    recordLamp: false
    # log记录 top.kx.kxss.controller/service/biz包下 所有方法的参数
    recordLampArgs: true
    # log记录 top.kx.kxss.controller/service/biz包下 所有方法的返回值
    recordLampResult: true
    enumPackage: top.kx.kxss.generator.enumeration,top.kx.kxss.model.enumeration
  ignore:
    # 是否启用网关的 uri权限鉴权 (设置为false，则后台不校验访问权限)
    authEnabled: true
    # 前端校验按钮 是否区分大小写
    caseSensitive: false
    anyone: # 请求中 需要携带Tenant 且 需要携带Token(不需要登录)，但不需要验证uri权限
      ALL:
        - /**/anyone/**
        - /service/model/*/json
        - /service/model/*/save
        - /service/editor/stencilset
        - /**/empProduct/**
        - /**/empSecurityCode/**
        - /**/basePurchase/**
        - /**/baseDistributor/**
        - /**/banner/**
        - /**/news/**
        - /**/pact/**
        - /**/question/**
        - /**/security/**
        - /**/member/**
        - /**/anyoneDict/**
        - /**/anyoneFile/**
        - /**/employee/**
        - /**/basePrintTemplate/**
        - /**/lotteryActivity/getChance
        - /**/lotteryActivity/lottery
        - /**/lotteryActivity/getPrizeList
    anyUser: # 请求中 需要携带Tenant，但 不需要携带Token(不需要登录) 和 不需要验证uri权限
      ALL:
        - /**/anyUser/**
    anyTenant: # 请求中 不需要携带Tenant 且 不需要携带Token(不需要登录) 和 不需要验证uri权限
      ALL:
        - /**/anyTenant/**
        - /**/anyoneWx/**
        - /findOnlineService
        - /**/pact/**
        - /**/banner/list
        - /**/security/check
        - /**/security/checkCode
        - /**/security/product
        - /**/security/qz/check
        - /**/security/qz/checkCode
        - /**/security/qz/product
        - /**/news/page
        - /**/news/detail
        - /**/question/page
        - /**/baseCustomers
        - /**/baseDistributor/refreshUserId
        - /**/baseDistributor/refreshPurchase
        - /**/anyoneWx/empAccountLogin
        - /**/basePact/query
        - /**/userBackground/getOneByAppKey
        - /**/security/qg/checkCode
        - /**/security/qg/product
  echo: #详情看: EchoProperties
    # 是否启用 远程数据 手动注入
    enabled: true
    # 是否启用 远程数据 注解AOP注入
    aop-enabled: true
    # 字典类型 和 code 的分隔符
    dictSeparator: '###'
    # 多个字典code 之间的的分隔符
    dictItemSeparator: ','
    # 递归最大深度
    maxDepth: 3
    # 本地缓存配置信息 生产慎用
    guavaCache:
      enabled: false
      maximumSize: 1000
      refreshWriteTime: 2
      refreshThreadPoolSize: 10
  log: # 详情看：OptLogProperties
    # 开启记录操作日志
    enabled: true
    # 记录到什么地方  DB:mysql LOGGER:日志文件
    type: DB
  xss:
    # 是否开启 xss 过滤器  详情看：XssProperties
    enabled: true
    # 是否启用 RequestBody 注解标记的参数 反序列化时过滤XSS
    requestBodyEnabled: false
    ignoreParamValues:
      - noxss
  captcha:
    # 登录界面的验证码配置 详情看：CaptchaProperties
    type: ARITHMETIC
    width: 158
    height: 58
    len: 2
    charType: 2
  async: # 全局线程池配置
    corePoolSize: 2
    maxPoolSize: 50
    queueCapacity: 10000
    keepAliveSeconds: 300
    threadNamePrefix: 'kxss-async-executor-'
  swagger:
    license: Powered By zuihou
    licenseUrl: https://github.com/zuihou
    termsOfServiceUrl: https://github.com/zuihou
    version: @project.version@
    contact: # 联系人信息
      url: https://github.com/zuihou
      name: zuiou
      email: <EMAIL>
    global-operation-parameters: # 全局参数
      - name: Token
        description: 用户信息
        modelRef: String
        parameterType: header
        required: true
        # 默认值只是方便本地开发时，少填参数，生产环境请禁用swagger或者禁用默认参数
        defaultValue: "test"
      - name: Authorization
        description: 客户端信息
        modelRef: String
        parameterType: header
        required: true
        defaultValue: "bGFtcF93ZWI6bGFtcF93ZWJfc2VjcmV0"
      - name: ApplicationId
        description: 应用ID
        modelRef: String
        parameterType: header
        required: true
        defaultValue: "1"
    docket:
      all:
        title: 所有接口
        base-package: top.kx.kxss
      # @kxss.generator auto insert docket.swagger
      oauth:
        title: 认证服务-认证模块
        base-package: top.kx.kxss.oauth.controller
      common:
        title: 基础服务-公共模块
        base-package: top.kx.kxss.base.controller.common
      user:
        title: 基础服务-用户模块
        base-package: top.kx.kxss.base.controller.user
      basesystem:
        title: 基础服务-系统模块
        base-package: top.kx.kxss.base.controller.system
      file:
        title: 基础服务-文件模块
        base-package: top.kx.kxss.file.controller
      msg:
        title: 基础服务-消息模块
        base-package: top.kx.kxss.msg.controller;top.kx.kxss.sms.controller
      tenant:
        title: 租户服务-租户模块
        base-package: top.kx.kxss.system.controller.tenant
      system:
        title: 租户服务-系统设置模块
        base-package: top.kx.kxss.system.controller.system
      appliction:
        title: 租户服务-应用资源模块
        base-package: top.kx.kxss.system.controller.application
      generator:
        title: 代码生成器服务
        base-package: top.kx.kxss.generator.controller
      activiti:
        title: 工作流服务
        base-package: top.kx.kxss.activiti.controller
      banner:
        title: 轮播图模块
        base-package: top.kx.kxss.base.controller.banner
      member:
        title: 会员模块
        base-package: top.kx.kxss.base.controller.member
      news:
        title: 新闻资讯模块
        base-package: top.kx.kxss.base.controller.news
      product:
        title: 商品模块
        base-package: top.kx.kxss.base.controller.product
      security:
        title: 防伪码模块
        base-package: top.kx.kxss.base.controller.security
      pact:
        title: 用户协议模块
        base-package: top.kx.kxss.base.controller.pact
      question:
        title: 常见问题模块
        base-package: top.kx.kxss.base.controller.question
      wxFile:
        title: 文件上传模块
        base-package: top.kx.kxss.file.controller.WxFileAnyoneController
      customers:
        title: 客户咨询模块
        base-package: top.kx.kxss.base.controller.customers
      distributor:
        title: 耗材经销商
        base-package: top.kx.kxss.base.controller.distributor
      purchase:
        title: 要货管理
        base-package: top.kx.kxss.base.controller.purchase

  generator:
    outputDir: /Users/<USER>/gitlab/kxss-boot-pro-none
    frontOutputDir: /Users/<USER>/gitlab/kxss-web-pro
    # 作者
    author: zuihou
    # 默认项目
    projectType: BOOT
    #  # 去除表前缀
    #  tablePrefix:
    #    - xxx_
    #  # 去除字段前缀
    #  fieldPrefix:
    #    - xxx_
    #  # 去除字段后缀
    #  fieldSuffix:
    #    - xxx_
    # 项目前缀
    projectPrefix: 'kxss'
    superClass: SUPER_CLASS
    # 生成方式
    genType: GEN
    packageInfoConfig: # 其他配置建议保持PackageInfoConfig中的默认值
      # 生成代码位于 src/main/java 下的基础包
      parent: 'top.kx.kxss'
      utilParent: 'top.kx.basic'
    entity-config:
      # 时间类型对应策略  ONLY_DATE: java.util  SQL_PACK:java.sql  TIME_PACK: java.time
      dateType: TIME_PACK
      # Entity类的父类
      entitySuperClass: ENTITY
      # 指定生成的主键的ID类型 (${superClass} == NONE 时，新生成的实体才生效)
      idType: INPUT
      # 数据库表字段映射到实体的命名策略
      columnNaming: underline_to_camel
      # 忽略字段（字段名）
      # ignoreColumns:
      #   - xxx
      # 【实体】 是否生成字段常量
      columnConstant: false
      # 【实体、VO】是否为链式模型
      chain: true
      # 【实体、VO】 是否为lombok模型
      lombok: true
      # 乐观锁字段名称
      versionColumnName: ''
      # 乐观锁属性名称
      versionPropertyName: ''
      # 逻辑删除字段名称
      logicDeleteColumnName: ''
      # 逻辑删除属性名称
      logicDeletePropertyName: ''
      #      fillColumnName:
      #        - xxx: INSERT
      # 格式化SaveVO文件名称
      formatSaveVoFileName: ''
      # 格式化UpdateVO文件名称
      formatUpdateVoFileName: ''
      # 格式化ResultVO文件名称
      formatResultVoFileName: ''
      # 格式化 PageQuery 文件名称
      formatPageQueryFileName: ''
    mapperConfig:
      formatMapperFileName: ''
      formatXmlFileName: ''
      mapperAnnotation: false
      #    columnAnnotationTablePrefix:
      #      - xxx
      baseResultMap: true
      cache: false
      baseColumnList: true
      cacheClass: org.apache.ibatis.cache.decorators.LoggingCache
    #  serviceConfig:
    #  managerConfig:
    #  controllerConfig:
    #  webProConfig:
    fileOverrideStrategy:
      entityFileOverride: OVERRIDE
      sqlFileOverride: OVERRIDE
      mapperFileOverride: EXIST_IGNORE
      xmlFileOverride: OVERRIDE
      managerFileOverride: EXIST_IGNORE
      serviceFileOverride: EXIST_IGNORE
      controllerFileOverride: EXIST_IGNORE
      apiModelFileOverride: OVERRIDE
      langFileOverride: OVERRIDE
      indexEditTreeFileOverride: EXIST_IGNORE
      dataFileOverride: EXIST_IGNORE
    constantsPackage:
      # 业务服务 后台手动改动过的枚举
      FieldFill: com.baomidou.mybatisplus.annotation.FieldFill
      SuperClassEnum: top.kx.kxss.generator.enumeration.SuperClassEnum
      EntitySuperClassEnum: top.kx.kxss.generator.enumeration.EntitySuperClassEnum
      # common 常量
      EchoDictType: top.kx.kxss.model.constant.EchoDictType
      EchoApi: top.kx.kxss.model.constant.EchoApi
      # common 公共枚举
      HttpMethod: top.kx.kxss.model.enumeration.HttpMethod
      BooleanEnum: top.kx.kxss.model.enumeration.BooleanEnum
      StateEnum: top.kx.kxss.model.enumeration.StateEnum
      UserStatusEnum: top.kx.kxss.model.enumeration.base.UserStatusEnum
      RoleCategoryEnum: top.kx.kxss.model.enumeration.base.RoleCategoryEnum
      ActiveStatusEnum: top.kx.kxss.model.enumeration.base.ActiveStatusEnum
      OrgTypeEnum: top.kx.kxss.model.enumeration.base.OrgTypeEnum
      FileType: top.kx.kxss.model.enumeration.base.FileType
      DateType: top.kx.kxss.model.enumeration.base.DateType
      DictClassifyEnum: top.kx.kxss.model.enumeration.system.DictClassifyEnum
      DataTypeEnum: top.kx.kxss.model.enumeration.system.DataTypeEnum
      CodeIdentifyEnum: top.kx.kxss.model.enumeration.base.CodeIdentifyEnum
      SecurityCodeStatusEnum: top.kx.kxss.model.enumeration.base.SecurityCodeStatusEnum
      SecurityCodeTypeEnum: top.kx.kxss.model.enumeration.base.SecurityCodeTypeEnum
      PopupTypeEnum: top.kx.kxss.generator.enumeration.PopupTypeEnum

#FAST_DFS配置
fdfs:
  soTimeout: 1500
  connectTimeout: 600
  thumb-image:
    width: 150
    height: 150
  tracker-list:
    - **************:22122
  pool:
    #从池中借出的对象的最大数目
    max-total: 153
    max-wait-millis: 102
    jmx-name-base: 1
    jmx-name-prefix: 1

spring:
  mvc:
    pathmatch:
      # 升级springboot2.6.6后临时处理，防止swagger报错
      matching-strategy: ANT_PATH_MATCHER
  config:
    activate:
      on-profile: common
  lifecycle:
    # 优雅停机宽限期时间
    timeout-per-shutdown-phase: 30s
  servlet:
    # 上传文件最大值
    multipart:
      max-file-size: 512MB
      max-request-size: 512MB
  freemarker:
    suffix: .ftl
    cache: false
    charset: UTF-8
    contentType: text/html
    requestContextAttribute: ctx
    templateEncoding: UTF-8
    templateLoaderPath: classpath:/
    settings:
      defaultEncoding: UTF-8
      url_escaping_charset: UTF-8
      locale: zh_CN
  activiti:
    database-schema-update: true
    async-executor-activate: false
    history-level: FULL
    check-process-definitions: false

# knife4j 文档通用配置 详情看: Knife4jProperties
knife4j:
  enable: true
  setting:
    enableReloadCacheParameter: true
    enableVersion: true
    enableDynamicParameter: true
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Apache License 2.0 | Copyright  2020 [kxss-cloud](https://github.com/zuihou)

# 通用dozer配置文件
dozer:
  mappingFiles:
    - classpath*:dozer/*.dozer.xml

management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
      enabled: true

feign:
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  hystrix:
    enabled: false
  sentinel:
    enabled: true
  client:
    config:
      default:
        # feign client 调用全局超时时间
        connectTimeout: 60000
        readTimeout: 60000
    #支持压缩的mime types
  compression: # 请求压缩
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 2048
    response: # 响应压缩
      enabled: true

---
#redis 配置
spring:
  cache:
    type: GENERIC
  redis:
    host: ${kxss.redis.ip}
    password: ${kxss.redis.password}
    port: ${kxss.redis.port}
    database: ${kxss.redis.database}
---
#rabbitmq 配置
spring:
  rabbitmq:
    enabled: ${kxss.rabbitmq.enabled}
    host: ${kxss.rabbitmq.ip}
    port: ${kxss.rabbitmq.port}
    username: ${kxss.rabbitmq.username}
    password: ${kxss.rabbitmq.password}
    listener:
      type: direct # simple direct

---
# mysql 通用配置
spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  datasource:
    dynamic:
      enabled: false
    druid:
      enable: true
      # 从这里开始(druid)，中间的这段配置用于 kxss.database.multiTenantType != DATASOURCE 时
      url: ${lamp.mysql.url}
      db-type: ${lamp.mysql.db-type}
      username: ${lamp.mysql.username}
      password: ${lamp.mysql.password}
      # 生产使用原生驱动，开发使用p6spy驱动打印日志
      driverClassName: ${lamp.mysql.driverClassName}
      initialSize: 10
      minIdle: 10
      maxActive: 200
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      validation-query: SELECT 'x'
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000  #配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      min-evictable-idle-time-millis: 300000    #配置一个连接在池中最小生存的时间，单位是毫秒
      filters: stat,wall
      filter:
        wall:
          enabled: true
          config:
            strictSyntaxCheck: false
            commentAllow: true
            multiStatementAllow: true
            noneBaseStatementAllow: true
        slf4j:
          enabled: true   # 使用slf4j打印可执行日志时，改成true
          statement-executable-sql-log-enable: true

      # 从这里结束(druid)，中间的这段配置用于 kxss.database.multiTenantType != DATASOURCE 时

      # 以下的2段配置，同时适用于所有模式
      web-stat-filter: # WebStatFilter配置，说明请参考Druid Wiki，配置_配置WebStatFilter
        enabled: true
        url-pattern: /*
        exclusions: "*.js , *.gif ,*.jpg ,*.png ,*.css ,*.ico , /druid/*"
        session-stat-max-count: 1000
        profile-enable: true
        session-stat-enable: false
      stat-view-servlet: #展示Druid的统计信息,StatViewServlet的用途包括：1.提供监控信息展示的html页面2.提供监控信息的JSON API
        enabled: true
        url-pattern: /druid/*   #根据配置中的url-pattern来访问内置监控页面，如果是上面的配置，内置监控页面的首页是/druid/index.html例如：http://192.168.0.108:9000/druid/index.html
        reset-enable: true    #允许清空统计数据
        login-username: ''
        login-password: ''
        allow: ''

mybatis-plus:
  mapper-locations:
    - classpath*:mapper_**/**/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: top.kx.kxss.*.entity;top.kx.basic.database.mybatis.typehandler
  typeEnumsPackage: top.kx.kxss.*.enumeration
  global-config:
    db-config:
      id-type: INPUT
      insert-strategy: NOT_NULL
      update-strategy: NOT_NULL
      where-strategy: NOT_EMPTY
      #全局逻辑删除的实体字段名
      logic-delete-field: delete_flag
      #逻辑已删除值(默认为 1)
      logic-delete-value: 1
      #逻辑未删除值(默认为 0)
      logic-not-delete-value: 0
  configuration:
    #配置返回数据库(column下划线命名&&返回java实体是驼峰命名)，自动匹配无需as（没开启这个，SQL需要写as： select user_id as userId）
    map-underscore-to-camel-case: true
    cache-enabled: false
    #配置JdbcTypeForNull, oracle数据库必须配置
    jdbc-type-for-null: 'null'
    default-enum-type-handler: top.kx.basic.database.mybatis.handlers.MybatisEnumTypeHandler
