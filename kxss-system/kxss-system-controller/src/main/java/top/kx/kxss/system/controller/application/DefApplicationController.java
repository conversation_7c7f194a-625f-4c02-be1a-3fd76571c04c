package top.kx.kxss.system.controller.application;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperCacheController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.system.entity.application.DefApplication;
import top.kx.kxss.system.service.application.DefApplicationService;
import top.kx.kxss.system.vo.query.application.DefApplicationPageQuery;
import top.kx.kxss.system.vo.result.application.ApplicationResourceResultVO;
import top.kx.kxss.system.vo.result.application.DefApplicationResultVO;
import top.kx.kxss.system.vo.save.application.DefApplicationSaveVO;
import top.kx.kxss.system.vo.update.application.DefApplicationUpdateVO;

import javax.validation.constraints.NotEmpty;
import java.util.List;

import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_STRING;
import static top.kx.kxss.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;


/**
 * <p>
 * 前端控制器
 * 应用
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defApplication")
@Api(value = "DefApplication", tags = "应用")
public class DefApplicationController extends SuperCacheController<DefApplicationService, Long, DefApplication, DefApplicationSaveVO, DefApplicationUpdateVO, DefApplicationPageQuery, DefApplicationResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "name", value = "名称", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测应用名是否重复", notes = "检测应用名是否重复")
    @GetMapping("/check")
    @WebLog(value = "检测应用名是否重复")
    public R<Boolean> check(@RequestParam(required = false) Long id, @NotEmpty(message = "应用名不能为空") @RequestParam String name) {
        return success(superService.check(id, name));
    }

    @ApiOperation(value = "查询应用资源列表", notes = "查询应用资源列表")
    @GetMapping("/findApplicationResourceList")
    @WebLog(value = "查询应用资源列表")
    public R<List<ApplicationResourceResultVO>> findApplicationResourceList() {
        return success(superService.findApplicationResourceList());
    }

    @ApiOperation(value = "查询可用的应用资源列表")
    @GetMapping("/findAvailableApplicationResourceList")
    @WebLog(value = "查询可用的应用资源列表")
    public R<List<ApplicationResourceResultVO>> findAvailableApplicationResourceList() {
        return success(superService.findAvailableApplicationResourceList());
    }

    @ApiOperation(value = "查询可用的应用数据权限列表")
    @GetMapping("/findAvailableApplicationDataScopeList")
    @WebLog(value = "查询可用的应用数据权限列表")
    public R<List<ApplicationResourceResultVO>> findAvailableApplicationDataScopeList() {
        return success(superService.findAvailableApplicationDataScopeList());
    }

}
