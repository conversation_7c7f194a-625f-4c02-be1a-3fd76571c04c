package top.kx.kxss.oauth.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.exception.BizException;
import top.kx.basic.jwt.TokenUtil;
import top.kx.basic.jwt.model.Token;
import top.kx.kxss.oauth.granter.TokenGranterBuilder;
import top.kx.kxss.oauth.service.UserInfoService;
import top.kx.kxss.oauth.vo.param.LoginParamVO;
import top.kx.kxss.oauth.vo.param.RegisterByEmailVO;
import top.kx.kxss.oauth.vo.param.RegisterByMobileVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;
import top.kx.kxss.system.service.tenant.DefUserService;

/**
 * 登录页 Controller
 *
 * <AUTHOR>
 * @date 2020年03月31日10:10:36
 */
@Slf4j
@RestController
@RequestMapping
@AllArgsConstructor
@Api(value = "登录页相关接口", tags = "登录-退出-注册")
public class RootController {

    private final TokenGranterBuilder tokenGranterBuilder;
    private final DefUserService defUserService;
    private final UserInfoService userInfoService;
    private final TokenUtil tokenUtil;

    /**
     * 登录接口
     * grantType 表示登录类型 可选值为：CAPTCHA,REFRESH_TOKEN,PASSWORD,MOBILE
     * <p>
     * CAPTCHA： 用户名 + 密码 + 图片验证码登录
     * key：验证码唯一字符窜
     * code：图片验证码
     * username：用户名
     * password：密码
     * <p>
     * PASSWORD：用户名 + 密码 登录
     * username：用户名
     * password：密码
     * <p>
     * MOBILE：手机号 + 短信验证码登录
     * username：手机号
     * code：短信验证码
     * <p>
     * REFRESH_TOKEN：刷新token （未实现）
     *
     * @param login login
     * @return top.kx.basic.base.R<top.kx.kxss.oauth.vo.result.LoginResultVO>
     * <AUTHOR>
     * @date 2022/9/29 9:33 PM
     * @create [2022/9/29 9:33 PM ] [tangyh] [初始创建]
     */
    @ApiOperation(value = "登录接口", notes = "登录或者清空缓存时调用")
    @PostMapping(value = "/anyTenant/login")
    public R<LoginResultVO> login(@Validated @RequestBody LoginParamVO login) throws BizException {
        return tokenGranterBuilder.getGranter(login.getGrantType()).login(login);
    }

    @ApiOperation(value = "退出", notes = "退出")
    @PostMapping(value = "/anyUser/logout")
    public R<Boolean> logout(String token) {
        return tokenGranterBuilder.getGranter().logout(token);
    }


    /**
     * 验证token
     */
    @ApiOperation(value = "验证token是否正确", notes = "验证token")
    @GetMapping(value = "/anyTenant/verify")
    public R<Token> verify(@RequestParam(value = "token") String token) throws BizException {
        return R.success(tokenUtil.parseToken(token));
    }

    /**
     * 注册
     */
    @ApiOperation(value = "根据手机号注册", notes = "根据手机号注册")
    @PostMapping(value = "/anyTenant/registerByMobile")
    public R<String> register(@Validated @RequestBody RegisterByMobileVO register) throws BizException {
        return R.success(userInfoService.registerByMobile(register));
    }

    @ApiOperation(value = "根据邮箱注册", notes = "根据邮箱注册")
    @PostMapping(value = "/anyTenant/registerByEmail")
    public R<String> register(@Validated @RequestBody RegisterByEmailVO register) throws BizException {
        return R.success(userInfoService.registerByEmail(register));
    }

    @ApiOperation(value = "检测手机号是否存在")
    @GetMapping("/anyTenant/checkMobile")
    public R<Boolean> checkMobile(@RequestParam String mobile) {
        return R.success(defUserService.checkMobile(mobile, null));
    }


}
