package top.kx.kxss.oauth.controller;

import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.binarywang.wx.miniapp.bean.scheme.WxMaGenerateSchemeRequest;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.BizException;
import top.kx.kxss.oauth.biz.OauthUserBiz;
import top.kx.kxss.oauth.vo.param.*;
import top.kx.kxss.oauth.vo.result.DefUserInfoResultVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;
import top.kx.kxss.userinfo.service.AppLoginService;
import top.kx.kxss.userinfo.service.wx.WxService;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 微信小程序登录 Controller
 *
 * <AUTHOR>
 * @date 2020年03月31日10:10:36
 */
@Slf4j
@RestController
@RequestMapping
@Api(value = "微信小程序登录相关接口", tags = "登录-授权")
public class WxAppController {

    @Resource
    private AppLoginService appLoginService;
    @Resource
    private OauthUserBiz oauthUserBiz;
    @Resource
    private WxService wxService;


    @ApiOperation("小程序手机号登录--用户端")
    @PostMapping("/anyoneWx/login")
    public R<LoginResultVO> login(@RequestBody @Validated AppPhoneLoginVO vo) {
        if (Objects.isNull(vo.getType())) {
            vo.setType(0);
        }
        return appLoginService.wxPhoneLogin(vo);
    }

    @ApiOperation("小程序手机号登录--员工端")
    @PostMapping("/anyoneWx/empLogin")
    public R<LoginResultVO> empLogin(@RequestBody @Validated AppPhoneLoginVO vo) {
        vo.setType(1);
        return appLoginService.wxPhoneLogin(vo);
    }

    @ApiOperation("小程序账号登录--员工端")
    @PostMapping("/anyoneWx/empAccountLogin")
    public R<LoginResultVO> empAccountLogin(@RequestBody @Validated AppAccountPhoneLoginVO vo) {
        vo.setType(1);
        return appLoginService.empAccountLogin(vo);
    }

    @ApiOperation("获取微信授权用户信息")
    @PostMapping("/anyoneWx/getWxInfo")
    public R<WxMaUserInfo> getWxInfo(@RequestBody AppLoginVO vo) {
        return R.success(appLoginService.getWxInfo(vo));
    }

    /**
     * 获取当前登录的用户信息
     */
    @ApiOperation(value = "获取当前登录的用户信息", notes = "获取当前登录的用户信息：登录后，查询用户信息")
    @GetMapping(value = "/anyone/getUserInfo")
    public R<DefUserInfoResultVO> getUserInfoById(@RequestParam(required = false) Long userId) throws BizException {
        if (userId == null) {
            userId = ContextUtil.getUserId();
        }
        return R.success(oauthUserBiz.getUserById(userId));
    }

    @ApiOperation(value = "获取URL Scheme", notes = "获取URL Scheme")
    @PostMapping(value = "/anyoneWx/generateScheme")
    public R<String> generateScheme(@RequestBody WxGenerateSchemeRequest request) {
        WxMaGenerateSchemeRequest request1 = BeanUtil.copyProperties(request, WxMaGenerateSchemeRequest.class);
        return R.success(wxService.generateScheme(request1, request.getClientId()));
    }

    @ApiOperation(value = "获取URL Link", notes = "获取URL Scheme")
    @PostMapping(value = "/anyoneWx/generateLink")
    public R<String> generateLink(@RequestBody WxGenerateUrlRequest request) {
        GenerateUrlLinkRequest request1 = BeanUtil.copyProperties(request, GenerateUrlLinkRequest.class);
        return R.success(wxService.generateLink(request1, request.getClientId()));
    }
}
