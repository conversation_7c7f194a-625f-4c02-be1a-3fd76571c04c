<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://wwwzhadmin.org/">
  <process id="my_leave" name="请假流程" isExecutable="true">
    <documentation>请假模型示例</documentation>
    <startEvent id="start" name="开始"></startEvent>
    <endEvent id="end" name="结束"></endEvent>
    <userTask id="QJ1" name="【提交申请】" activiti:assignee="#{USERNAME}">
      <extensionElements>
        <activiti:taskListener event="create" class="top.kx.kxss.activiti.listener.ManagerTaskHandler"></activiti:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="QJ2" name="【部门经理审批】" activiti:assignee="4">
      <extensionElements>
        <activiti:taskListener event="create" class="top.kx.kxss.activiti.listener.ManagerTaskHandler"></activiti:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="QJ3" name="【总经理审批】" activiti:assignee="2">
      <extensionElements>
        <activiti:taskListener event="create" class="top.kx.kxss.activiti.listener.ManagerTaskHandler"></activiti:taskListener>
        <modeler:initiator-can-complete xmlns:modeler="http://activiti.com/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="pt1" name="批准/驳回"></exclusiveGateway>
    <exclusiveGateway id="pt2" name="批准/驳回"></exclusiveGateway>
    <sequenceFlow id="flow1" name="启动" sourceRef="start" targetRef="QJ1"></sequenceFlow>
    <sequenceFlow id="flow3" sourceRef="QJ2" targetRef="pt1"></sequenceFlow>
    <sequenceFlow id="flow2" name="提交" sourceRef="QJ1" targetRef="QJ2"></sequenceFlow>
    <sequenceFlow id="flow4" name="驳回" sourceRef="pt1" targetRef="QJ1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${RESULT == "驳回"}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow8" name="批准" sourceRef="pt2" targetRef="end">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${RESULT == "批准"}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow7" name="驳回" sourceRef="pt2" targetRef="QJ1">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${RESULT == "驳回"}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow5" name="批准" sourceRef="pt1" targetRef="QJ3">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${RESULT == "批准"}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow6" sourceRef="QJ3" targetRef="pt2"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_my_leave">
    <bpmndi:BPMNPlane bpmnElement="my_leave" id="BPMNPlane_my_leave">
      <bpmndi:BPMNShape bpmnElement="start" id="BPMNShape_start">
        <omgdc:Bounds height="30.0" width="30.0" x="30.0" y="130.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
        <omgdc:Bounds height="28.0" width="28.0" x="750.0" y="231.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="QJ1" id="BPMNShape_QJ1">
        <omgdc:Bounds height="80.0" width="100.0" x="150.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="QJ2" id="BPMNShape_QJ2">
        <omgdc:Bounds height="80.0" width="100.0" x="345.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="QJ3" id="BPMNShape_QJ3">
        <omgdc:Bounds height="80.0" width="100.0" x="645.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="pt1" id="BPMNShape_pt1">
        <omgdc:Bounds height="40.0" width="40.0" x="510.0" y="30.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="pt2" id="BPMNShape_pt2">
        <omgdc:Bounds height="40.0" width="40.0" x="510.0" y="225.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
        <omgdi:waypoint x="60.0" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="150.0" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
        <omgdi:waypoint x="250.0" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="345.0" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow3" id="BPMNEdge_flow3">
        <omgdi:waypoint x="445.0" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="530.0" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="530.0" y="70.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4">
        <omgdi:waypoint x="510.0" y="50.0"></omgdi:waypoint>
        <omgdi:waypoint x="200.0" y="50.0"></omgdi:waypoint>
        <omgdi:waypoint x="200.0" y="105.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow5" id="BPMNEdge_flow5">
        <omgdi:waypoint x="530.0" y="70.0"></omgdi:waypoint>
        <omgdi:waypoint x="530.0" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="645.0" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow6" id="BPMNEdge_flow6">
        <omgdi:waypoint x="695.0" y="185.0"></omgdi:waypoint>
        <omgdi:waypoint x="695.0" y="245.0"></omgdi:waypoint>
        <omgdi:waypoint x="550.0" y="245.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow7" id="BPMNEdge_flow7">
        <omgdi:waypoint x="510.0" y="245.0"></omgdi:waypoint>
        <omgdi:waypoint x="200.0" y="245.0"></omgdi:waypoint>
        <omgdi:waypoint x="200.0" y="185.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="flow8" id="BPMNEdge_flow8">
        <omgdi:waypoint x="550.0" y="245.0"></omgdi:waypoint>
        <omgdi:waypoint x="750.0" y="245.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
