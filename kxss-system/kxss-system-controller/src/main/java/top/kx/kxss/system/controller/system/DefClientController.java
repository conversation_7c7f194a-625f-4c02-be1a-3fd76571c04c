package top.kx.kxss.system.controller.system;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.service.system.DefClientService;
import top.kx.kxss.system.vo.query.system.DefClientPageQuery;
import top.kx.kxss.system.vo.result.system.DefClientResultVO;
import top.kx.kxss.system.vo.save.system.DefClientSaveVO;
import top.kx.kxss.system.vo.update.system.DefClientUpdateVO;


/**
 * <p>
 * 前端控制器
 * 客户端
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defClient")
@Api(value = "DefClient", tags = "客户端")
public class DefClientController extends SuperController<DefClientService, Long, DefClient, DefClientSaveVO, DefClientUpdateVO, DefClientPageQuery, DefClientResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}
