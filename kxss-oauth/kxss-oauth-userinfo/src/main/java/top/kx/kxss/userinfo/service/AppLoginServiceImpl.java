package top.kx.kxss.userinfo.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.common.properties.WxMaProperties;
import top.kx.kxss.oauth.enumeration.GrantType;
import top.kx.kxss.oauth.granter.TokenGranterBuilder;
import top.kx.kxss.oauth.vo.param.AppAccountPhoneLoginVO;
import top.kx.kxss.oauth.vo.param.AppLoginVO;
import top.kx.kxss.oauth.vo.param.AppPhoneLoginVO;
import top.kx.kxss.oauth.vo.param.LoginParamVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.service.system.DefClientService;
import top.kx.kxss.userinfo.config.WxMaConfiguration;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AppLoginServiceImpl implements AppLoginService {

    @Resource
    private TokenGranterBuilder tokenGranterBuilder;
    @Resource
    private WxMaProperties wxMaProperties;
    @Resource
    private DefClientService defClientService;


    @Override
    public WxMaUserInfo getWxInfo(AppLoginVO entity) {
        ArgumentAssert.notBlank(entity.getCode(), "code不能为空");
        WxMaProperties.Config wxConfig = wxMaProperties.getConfigs().get(0);
        if (StrUtil.isNotBlank(ContextUtil.getClientId())) {
            List<WxMaProperties.Config> collect = wxMaProperties.getConfigs().stream().filter(wxConfig1 -> wxConfig1.getClientId()
                    .equals(ContextUtil.getClientId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                wxConfig = collect.get(0);
            }
        }
        //获取小程序配置信息
        //小程序appid
        String appid = wxConfig.getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appid);
        try {
            // code换取session
            WxMaJscode2SessionResult session = wxService.getUserService().getSessionInfo(entity.getCode());
            log.info("code换取session：{}", session);
            // 用户信息校验
            boolean userFlag = wxService.getUserService().checkUserInfo(session.getSessionKey(), entity.getRawData(), entity.getSignature());
            ArgumentAssert.isFalse(!userFlag, "用户信息校验失败");
            // 解密用户信息
            WxMaUserInfo userInfo = wxService.getUserService().getUserInfo(session.getSessionKey(), entity.getEncryptedData(), entity.getIv());
            log.info("解密用户信息：{}", userInfo);
            //处理业务

            return userInfo;
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public R<LoginResultVO> wxPhoneLogin(AppPhoneLoginVO entity) {
        ArgumentAssert.notBlank(entity.getCode(), "code不能为空");
        //获取小程序配置信息
        WxMaProperties.Config wxConfig = wxMaProperties.getConfigs().get(entity.getType());
        if (StrUtil.isNotBlank(ContextUtil.getClientId())) {
            List<WxMaProperties.Config> collect = wxMaProperties.getConfigs().stream().filter(wxConfig1 -> wxConfig1.getClientId()
                    .equals(ContextUtil.getClientId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                wxConfig = collect.get(0);
            }
        }
        //小程序appid
        String appid = wxConfig.getAppid();
        final WxMaService wxService = WxMaConfiguration.getMaService(appid);
        try {
            // code换取session
            WxMaJscode2SessionResult session = wxService.getUserService().getSessionInfo(entity.getJsCode());
            log.info("code换取session：{}", session);

            // 解密用户信息
            WxMaPhoneNumberInfo phoneNoInfo = wxService.getUserService().getPhoneNoInfo(entity.getCode());
            log.info("获取手机号信息：{}", phoneNoInfo);

            //处理业务
            LoginParamVO login = LoginParamVO.builder()
                    .grantType(grantType(entity.getType()))
                    .mobile(phoneNoInfo.getPhoneNumber())
                    .openId(session.getOpenid())
                    .clientId(wxConfig.getClientId())
                    .clientSecret(wxConfig.getClientSecret())
                    .build();
            return tokenGranterBuilder.getGranter(login.getGrantType()).wxLogin(login);
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public R<LoginResultVO> empAccountLogin(AppAccountPhoneLoginVO entity) {
        ArgumentAssert.notNull(ContextUtil.getClientId(), "请求头参数异常！");
        ArgumentAssert.notBlank(entity.getUsername(), "请输入账号");
        ArgumentAssert.notBlank(entity.getPassword(), "请输入密码");
        DefClient defClient = defClientService.getSuperManager().getOne(Wraps.<DefClient>lbQ()
                .eq(DefClient::getClientId, ContextUtil.getClientId()).eq(DefClient::getState, true));
        ArgumentAssert.notNull(defClient, "应用未开放！");
        //处理业务
        LoginParamVO login = LoginParamVO.builder()
                .grantType(GrantType.WX_EMPLOYEE_PASSWORD)
                .mobile(entity.getUsername())
                .username(entity.getUsername())
                .password(entity.getPassword())
                .clientId(defClient.getClientId())
                .clientSecret(defClient.getClientSecret())
                .build();
        return tokenGranterBuilder.getGranter(login.getGrantType()).wxLogin(login);
    }


    private GrantType grantType(Integer type) {
        switch (type) {
            case 0:
            case 2:
            case 3:
                return GrantType.WX_MOBILE;
            case 1:
                return GrantType.WX_EMPLOYEE_MOBILE;
            default:
                return null;
        }
    }
}
