package top.kx.kxss.activiti.service.activiti;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.converter.BpmnXMLConverter;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.editor.constants.ModelDataJsonConstants;
import org.activiti.editor.language.json.converter.BpmnJsonConverter;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntityImpl;
import org.activiti.engine.impl.persistence.entity.SuspensionState;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.DeploymentBuilder;
import org.activiti.engine.repository.DeploymentQuery;
import org.activiti.engine.repository.Model;
import org.activiti.engine.repository.ModelQuery;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.apache.commons.io.IOUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.exception.BizException;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.StrHelper;
import top.kx.kxss.activiti.vo.ProcessDefinitionVO;

import javax.servlet.http.HttpServletResponse;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamException;
import javax.xml.stream.XMLStreamReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipInputStream;

/**
 * 流程定义业务
 *
 * <AUTHOR>
 * @date 2020-08-07
 */
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
@RequiredArgsConstructor
public class LampProcessDefinitionService {

    private final RepositoryService repositoryService;

    /**
     * 分页查询流程定义文件
     *
     * @param pageParams 分页入参
     */
    @Transactional(readOnly = true)
    public IPage<ProcessDefinitionVO> page(PageParams<ProcessDefinitionVO> pageParams) {
        IPage<ProcessDefinitionVO> page = pageParams.buildPage();
        ProcessDefinitionVO processDefinitionDO = pageParams.getModel();

        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        processDefinitionQuery.orderByProcessDefinitionId().desc().orderByProcessDefinitionVersion().desc();
        if (StrUtil.isNotBlank(processDefinitionDO.getName())) {
            processDefinitionQuery.processDefinitionNameLike(StrHelper.fullLike(processDefinitionDO.getName()));
        }
        if (StrUtil.isNotBlank(processDefinitionDO.getKey())) {
            processDefinitionQuery.processDefinitionKeyLike(StrHelper.fullLike(processDefinitionDO.getKey()));
        }
        if (StrUtil.isNotBlank(processDefinitionDO.getCategory())) {
            processDefinitionQuery.processDefinitionCategoryLike(StrHelper.fullLike(processDefinitionDO.getCategory()));
        }

        page.setTotal(processDefinitionQuery.count());
        if (page.getTotal() > 0) {
            List<ProcessDefinition> processDefinitionList = processDefinitionQuery.listPage((int) page.offset(), (int) page.getSize());

            List<ProcessDefinitionVO> list = new ArrayList<>();
            for (ProcessDefinition definition : processDefinitionList) {
                ProcessDefinitionEntityImpl entityImpl = (ProcessDefinitionEntityImpl) definition;
                ProcessDefinitionVO entity = BeanUtil.toBean(definition, ProcessDefinitionVO.class);
                DeploymentQuery deploymentQuery = repositoryService.createDeploymentQuery().deploymentId(definition.getDeploymentId());
                Deployment deployment = deploymentQuery.singleResult();
                entity.setDeploymentId(deployment.getId());
                entity.setDeploymentTime(deployment.getDeploymentTime());
                entity.setDeploymentName(deployment.getName());
                entity.setSuspensionState(entityImpl.getSuspensionState());
                if (entityImpl.getSuspensionState() == SuspensionState.ACTIVE.getStateCode()) {
                    entity.setSuspensionStateName("已激活");
                } else {
                    entity.setSuspensionStateName("已挂起");
                }
                list.add(entity);
            }
            page.setRecords(list);
        }
        return page;
    }


    /**
     * 通过流程定义映射模型
     *
     * @param processDefinitionId 流程定义id
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveModelByProcessDefinition(String processDefinitionId) {
        ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery();
        processDefinitionQuery.processDefinitionId(processDefinitionId);
        ProcessDefinition processDefinition = processDefinitionQuery.singleResult();
        ArgumentAssert.notNull(processDefinition, "流程定义不存在");

        InputStream bpmnStream = repositoryService.getResourceAsStream(processDefinition.getDeploymentId(), processDefinition.getResourceName());
        XMLInputFactory xif = XMLInputFactory.newInstance();
        InputStreamReader in;
        XMLStreamReader xtr = null;
        try {
            in = new InputStreamReader(bpmnStream, StandardCharsets.UTF_8);
            xtr = xif.createXMLStreamReader(in);
        } catch (XMLStreamException e) {
            throw BizException.wrap("文件读取失败");
        }
        BpmnModel bpmnModel = new BpmnXMLConverter().convertToBpmnModel(xtr);
        BpmnJsonConverter converter = new BpmnJsonConverter();
        ObjectNode modelNode = converter.convertToJson(bpmnModel);

        Model modelData = repositoryService.newModel();
        modelData.setKey(processDefinition.getKey());
        modelData.setCategory(processDefinition.getCategory());
        modelData.setName(processDefinition.getName() + "(映射)");
        modelData.setDeploymentId(processDefinition.getDeploymentId());
        ModelQuery modelQuery = repositoryService.createModelQuery().modelKey(modelData.getKey());
        long keyCount = modelQuery.count();
        modelData.setVersion(Convert.toInt(keyCount + 1));

        JSONObject meta = JSONUtil.createObj();
        meta.set(ModelDataJsonConstants.MODEL_NAME, processDefinition.getName());
        meta.set(ModelDataJsonConstants.MODEL_REVISION, modelData.getVersion());
        meta.set(ModelDataJsonConstants.MODEL_DESCRIPTION, processDefinition.getDescription());
        modelData.setMetaInfo(meta.toString());

        repositoryService.saveModel(modelData);
        repositoryService.addModelEditorSource(modelData.getId(), modelNode.toString().getBytes(StandardCharsets.UTF_8));
        return true;
    }


    /**
     * 修改流程定义状态
     *
     * @param processDefinitionId 流程定义ID
     * @param suspensionState     状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateState(String processDefinitionId, Integer suspensionState) {
        ProcessDefinitionQuery query = repositoryService.createProcessDefinitionQuery();
        query.processDefinitionId(processDefinitionId);
        ProcessDefinition processDefinition = query.singleResult();
        ArgumentAssert.notNull(processDefinition, "流程不存在");

        if (suspensionState == SuspensionState.ACTIVE.getStateCode()) {
            repositoryService.suspendProcessDefinitionById(processDefinitionId);
        } else {
            repositoryService.activateProcessDefinitionById(processDefinitionId);
        }
    }


    /**
     * 删除流程定义
     *
     * @param deploymentIdsArr 部署id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteProcessDeploymentByIds(List<String> deploymentIdsArr) {
        for (String deploymentId : deploymentIdsArr) {
            repositoryService.deleteDeployment(deploymentId, true);
        }
        return true;
    }

    /**
     * 通过ZIP部署流程定义
     *
     * @param name 文件名
     * @param in   地址
     */
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public String deploymentDefinitionByZip(String name, InputStream in) {
        try (ZipInputStream zis = new ZipInputStream(in)) {
            DeploymentBuilder deploymentBuilder = repositoryService.createDeployment();
            Deployment deployment = deploymentBuilder
                    .addZipInputStream(zis)
                    .name(name)
                    //该函数将DeploymentBuilder isDuplicateFilterEnabled 属性设置为 true, 在部署时会检测已部署的相同文件的最后一条记录，如果内容相同，则不会部署
//                    .enableDuplicateFiltering()
                    .deploy();
            return deployment.getId();
        }
    }

    /**
     * 预览流程定义XML
     *
     * @param processDefinitionId 流程定义id
     */
    @Transactional(readOnly = true)
    @SneakyThrows
    public String getXml(String processDefinitionId) {
        ProcessDefinitionQuery pdq = repositoryService.createProcessDefinitionQuery();
        ProcessDefinition pd = pdq.processDefinitionId(processDefinitionId).singleResult();
        ArgumentAssert.notNull(pd, "流程不存在");

        // 通过接口读取
        InputStream resourceAsStream = repositoryService.getResourceAsStream(pd.getDeploymentId(), pd.getResourceName());

        return IOUtils.toString(resourceAsStream, StandardCharsets.UTF_8);
    }

    /**
     * 读取流程资源
     *
     * @param processDefinitionId 流程定义ID
     */
    @Transactional(readOnly = true)
    public void getDiagram(String processDefinitionId, HttpServletResponse response) {
        ProcessDefinitionQuery pdq = repositoryService.createProcessDefinitionQuery();
        ProcessDefinition pd = pdq.processDefinitionId(processDefinitionId)
                .singleResult();
        ArgumentAssert.notNull(pd, "流程不存在");

//        setHeader(response);
        // 输出资源内容到相应对 象
        try (InputStream in = repositoryService.getResourceAsStream(pd.getDeploymentId(), pd.getDiagramResourceName())) {
            IoUtil.copy(in, response.getOutputStream());
            response.getOutputStream().flush();
        } catch (IOException e) {
            throw BizException.wrap("文件读取失败");
        }
    }

    private void setHeader(HttpServletResponse response) {
        response.setContentType("image/png");
        response.setHeader(HttpHeaders.PRAGMA, "No-cache");
        response.setHeader(HttpHeaders.CACHE_CONTROL, "No-cache");
        response.setDateHeader(HttpHeaders.EXPIRES, 0L);
    }
}
