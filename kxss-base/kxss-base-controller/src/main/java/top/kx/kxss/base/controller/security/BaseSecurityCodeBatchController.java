package top.kx.kxss.base.controller.security;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.security.BaseSecurityCodeBatch;
import top.kx.kxss.base.service.security.BaseSecurityCodeBatchService;
import top.kx.kxss.base.vo.query.security.BaseSecurityCodeBatchPageQuery;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchDetailsExportResultVO;
import top.kx.kxss.base.vo.result.security.BaseSecurityCodeBatchResultVO;
import top.kx.kxss.base.vo.save.security.BaseSecurityCodeBatchSaveVO;
import top.kx.kxss.base.vo.update.security.BaseSecurityCodeBatchUpdateVO;
import top.kx.kxss.model.enumeration.base.SecurityCodeBatchImportEnum;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 前端控制器
 * 防伪码批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-04-25 17:45:12
 * @create [2025-04-25 17:45:12] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseSecurityCodeBatch")
@Api(value = "BaseSecurityCodeBatch", tags = "防伪码批次")
public class BaseSecurityCodeBatchController extends SuperController<BaseSecurityCodeBatchService, Long, BaseSecurityCodeBatch, BaseSecurityCodeBatchSaveVO,
    BaseSecurityCodeBatchUpdateVO, BaseSecurityCodeBatchPageQuery, BaseSecurityCodeBatchResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation(value = "一键导入到防伪数据库中")
    @PostMapping("/importSecurityCode")
    public R<Boolean> importSecurityCode(@RequestParam Long batchId) {
        return success(superService.importSecurityCode(batchId));
    }

    @ApiOperation(value = "防伪码-导出", notes = "防伪码-导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void securityCodeBatchExport(@RequestBody @Validated BaseSecurityCodeBatchPageQuery query, HttpServletResponse response) {
        if (Objects.isNull(query) || Objects.isNull(query.getId())) {
            ArgumentAssert.isTrue(false, "请选择需要导出的批次");
        }
        List<BaseSecurityCodeBatchDetailsExportResultVO> resultVOList = superService.exportSecurityCode(query.getId());
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=SECURITY_CODE.xlsx");

        ExportParams params = new ExportParams("SECURITY_CODE", "SECURITY_CODE");
        try (Workbook workbook = ExcelExportUtil.exportExcel(params, BaseSecurityCodeBatchDetailsExportResultVO.class, resultVOList)){
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            log.error("导出失败", e);
            ArgumentAssert.isTrue(false, "导出失败");
        }
    }

    @Override
    public R<Boolean> delete(List<Long> longs) {
        // 查询有没有删除中
        List<BaseSecurityCodeBatch> baseSecurityCodeBatchList = superService.listByIds(longs);
        // 导入中的不能删除
        ArgumentAssert.isFalse(baseSecurityCodeBatchList.stream().anyMatch(baseSecurityCodeBatch -> StringUtils.equals(baseSecurityCodeBatch.getImportState(), SecurityCodeBatchImportEnum.IMPORTING.getCode())), "存在导入中的批次不能删除");
        // 已经导入的不能删除
        ArgumentAssert.isFalse(baseSecurityCodeBatchList.stream().anyMatch(baseSecurityCodeBatch -> StringUtils.equals(baseSecurityCodeBatch.getImportState(), SecurityCodeBatchImportEnum.IMPORTED.getCode())), "防伪码已经导入不能删除");
        return super.delete(longs);
    }
}


