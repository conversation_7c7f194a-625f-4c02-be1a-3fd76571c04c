package top.kx.kxss.system.controller.system;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperCacheController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.system.entity.system.DefParameter;
import top.kx.kxss.system.service.system.DefParameterService;
import top.kx.kxss.system.vo.query.system.DefParameterPageQuery;
import top.kx.kxss.system.vo.result.system.DefParameterResultVO;
import top.kx.kxss.system.vo.save.system.DefParameterSaveVO;
import top.kx.kxss.system.vo.update.system.DefParameterUpdateVO;

import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_STRING;
import static top.kx.kxss.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;


/**
 * <p>
 * 前端控制器
 * 参数配置
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defParameter")
@Api(value = "DefParameter", tags = "参数配置")
public class DefParameterController extends SuperCacheController<DefParameterService, Long, DefParameter, DefParameterSaveVO, DefParameterUpdateVO, DefParameterPageQuery, DefParameterResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public QueryWrap<DefParameter> handlerWrapper(DefParameter model, PageParams<DefParameterPageQuery> params) {
        QueryWrap<DefParameter> wrap = super.handlerWrapper(null, params);
        LbQueryWrap<DefParameter> wrapper = wrap.lambda();
        wrapper.like(DefParameter::getKey, model.getKey())
                .like(DefParameter::getName, model.getName())
                .like(DefParameter::getValue, model.getValue())
                .like(DefParameter::getRemarks, model.getRemarks())
                .in(DefParameter::getState, params.getModel().getState());
        return wrap;
    }


    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "key", value = "参数键", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测参数键是否可用")
    @GetMapping("/check")
    public R<Boolean> check(@RequestParam String key, @RequestParam(required = false) Long id) {
        return success(superService.checkKey(key, id));
    }
}
