package top.kx.kxss.msg.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.msg.entity.DefInterfaceProperty;
import top.kx.kxss.msg.service.DefInterfacePropertyService;
import top.kx.kxss.msg.vo.query.DefInterfacePropertyPageQuery;
import top.kx.kxss.msg.vo.result.DefInterfacePropertyResultVO;
import top.kx.kxss.msg.vo.save.DefInterfacePropertyBatchSaveVO;
import top.kx.kxss.msg.vo.save.DefInterfacePropertySaveVO;
import top.kx.kxss.msg.vo.update.DefInterfacePropertyUpdateVO;

/**
 * <p>
 * 前端控制器
 * 接口属性
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 15:51:37
 * @create [2022-07-04 15:51:37] [zuihou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/defInterfaceProperty")
@Api(value = "DefInterfaceProperty", tags = "接口属性")
public class DefInterfacePropertyController extends SuperController<DefInterfacePropertyService, Long, DefInterfaceProperty, DefInterfacePropertySaveVO,
        DefInterfacePropertyUpdateVO, DefInterfacePropertyPageQuery, DefInterfacePropertyResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    /**
     * 新增
     *
     * @param saveVO 保存参数
     * @return 实体
     */
    @ApiOperation(value = "保存")
    @PostMapping("/batchSave")
    @WebLog(value = "保存", request = false)
    public R<Boolean> batchSave(@RequestBody @Validated DefInterfacePropertyBatchSaveVO saveVO) {
        return R.success(superService.batchSave(saveVO));

    }

}


