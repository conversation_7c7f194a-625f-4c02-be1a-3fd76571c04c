package top.kx.kxss.system.controller.system;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.system.controller.system.domain.Server;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/6/29 2:24 PM
 * @create [2022/6/29 2:24 PM ] [tangyh] [初始创建]
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defServer")
@Api(value = "DefServer", tags = "服务监控")
public class DefServerController {
    @GetMapping()
    public R<Server> server() {
        Server server = new Server();
        server.copyTo();
        return R.success(server);
    }

}
