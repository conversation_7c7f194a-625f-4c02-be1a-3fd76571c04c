package top.kx.kxss.generator.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.kxss.generator.entity.DefGenTableColumn;
import top.kx.kxss.generator.vo.query.DefGenTableColumnPageQuery;
import top.kx.kxss.generator.vo.result.DefGenTableColumnResultVO;
import top.kx.kxss.generator.vo.save.DefGenTableColumnSaveVO;
import top.kx.kxss.generator.vo.update.DefGenTableColumnUpdateVO;

/**
 * <p>
 * 业务接口
 * 代码生成字段
 * </p>
 *
 * <AUTHOR>
 * @date 2022-03-01
 */
public interface DefGenTableColumnService extends SuperService<Long, DefGenTableColumn, DefGenTableColumnSaveVO, DefGenTableColumnUpdateVO, DefGenTableColumnPageQuery, DefGenTableColumnResultVO> {

    /**
     * 分页查询指定表的字段
     *
     * @param params params
     * @return com.baomidou.mybatisplus.core.metadata.IPage<top.kx.kxss.generator.vo.result.DefGenTableColumnResultVO>
     * <AUTHOR>
     * @date 2022/10/28 4:54 PM
     * @create [2022/10/28 4:54 PM ] [tangyh] [初始创建]
     */
    IPage<DefGenTableColumnResultVO> pageColumn(PageParams<DefGenTableColumnPageQuery> params);

    /**
     * 同步字段结构和注释
     *
     * @param id      id
     * @param tableId
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/3/26 11:19 AM
     * @create [2022/3/26 11:19 AM ] [tangyh] [初始创建]
     */
    Boolean syncField(Long tableId, Long id);
}
