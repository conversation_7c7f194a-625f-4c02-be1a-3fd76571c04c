package top.kx.kxss.generator.converts;

import top.kx.kxss.generator.config.DateType;
import top.kx.kxss.generator.rules.ColumnType;
import top.kx.kxss.generator.rules.DbColumnType;

import static top.kx.kxss.generator.rules.DbColumnType.BIG_DECIMAL;
import static top.kx.kxss.generator.rules.DbColumnType.BLOB;
import static top.kx.kxss.generator.rules.DbColumnType.BOOLEAN;
import static top.kx.kxss.generator.rules.DbColumnType.BYTE_ARRAY;
import static top.kx.kxss.generator.rules.DbColumnType.CLOB;
import static top.kx.kxss.generator.rules.DbColumnType.DOUBLE;
import static top.kx.kxss.generator.rules.DbColumnType.FLOAT;
import static top.kx.kxss.generator.rules.DbColumnType.INTEGER;
import static top.kx.kxss.generator.rules.DbColumnType.LONG;
import static top.kx.kxss.generator.rules.DbColumnType.STRING;

/**
 * <AUTHOR>
 * @date 2022/3/13 23:02
 */
public class MySqlTypeConvert implements ITypeConvert {
    public static final MySqlTypeConvert INSTANCE = new MySqlTypeConvert();

    /**
     * 转换为日期类型
     *
     * @param dt   日期类型
     * @param type 类型
     * @return 返回对应的列类型
     */
    public static ColumnType toDateType(DateType dt, String type) {
        String dateType = type.replaceAll("\\(\\d+\\)", "");
        switch (dt) {
            case ONLY_DATE:
                return DbColumnType.DATE;
            case SQL_PACK:
                switch (dateType) {
                    case "date":
                    case "year":
                        return DbColumnType.DATE_SQL;
                    case "time":
                        return DbColumnType.TIME;
                    default:
                        return DbColumnType.TIMESTAMP;
                }
            case TIME_PACK:
                switch (dateType) {
                    case "date":
                        return DbColumnType.LOCAL_DATE;
                    case "time":
                        return DbColumnType.LOCAL_TIME;
                    case "year":
                        return DbColumnType.YEAR;
                    default:
                        return DbColumnType.LOCAL_DATE_TIME;
                }
            default:
                return STRING;
        }
    }

    /**
     * @inheritDoc
     */
    @Override
    public ColumnType processTypeConvert(DateType datetype, String fieldType, Long size, Integer digit) {
        return TypeConverts.use(fieldType)
                .test(TypeConverts.containsAny("longtext", "char", "text", "json", "enum").then(STRING))
                .test(TypeConverts.contains("bigint").then(LONG))
                .test(TypeConverts.containsAny("tinyint(1)", "bit(1)").then(BOOLEAN))
                .test(TypeConverts.contains("bit").then(BOOLEAN))
                .test(TypeConverts.contains("int").then(INTEGER))
                .test(TypeConverts.contains("decimal").then(BIG_DECIMAL))
                .test(TypeConverts.contains("clob").then(CLOB))
                .test(TypeConverts.contains("blob").then(BLOB))
                .test(TypeConverts.contains("binary").then(BYTE_ARRAY))
                .test(TypeConverts.contains("float").then(FLOAT))
                .test(TypeConverts.contains("double").then(DOUBLE))
                .test(TypeConverts.containsAny("date", "time", "year")
                        .then(t -> toDateType(datetype, t)))
                .or(STRING);
    }
}
