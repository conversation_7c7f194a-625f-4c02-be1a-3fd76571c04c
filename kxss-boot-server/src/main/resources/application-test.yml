kxss:
  cache:
    type: REDIS
  redis:
    ip: *************
    port: 6032
    password: uFgwDY#JyP]
    database: 2
  rabbitmq:
    enabled: false
    ip: *************
    port: 5672
    username: kxss
    password: kxss
  miniapp:
    configs:
      - appid: #微信小程序的appid
        secret: #微信小程序的Secret
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON

  database:
    isNotWrite: true
  swagger:
    # 正式环境用nginx代理，为了保持和cloud版本一致的URL，特意调整
    docket:
      # @kxss.generator auto insert docket.path
      activiti:
        base-path: /api/activiti
      oauth:
        base-path: /api/oauth
      common:
        base-path: /api/base
      user:
        base-path: /api/base
      basesystem:
        base-path: /api/oauth
      msg:
        base-path: /api/base
      file:
        base-path: /api/base
      system:
        base-path: /api/base
      appliction:
        base-path: /api/base
      tenant:
        base-path: /api/system
      generator:
        base-path: /api/generator
  file:
    storageType: FAST_DFS  #  FAST_DFS LOCAL MIN_IO ALI_OSS HUAWEI_OSS QINIU_OSS
    publicBucket:
      - public

# 相当于 database.yml
lamp:
  mysql:
    filters: stat,wall
    db-type: mysql
    validation-query: SELECT 'x'
    username: 'root'
    password: 'e76js7026f'
    # 生产使用原生驱动，开发使用p6spy驱动打印日志
    driverClassName: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************************************************************************************
  database: # 字段介绍参考 DatabaseProperties
    # 4.x 需要写死 column， 其他模式需要使用其他项目，而非改变此参数
    multiTenantType: NONE
    # 是否不允许写入数据  WriteInterceptor
    isNotWrite: false
    # 是否启用  sql性能规范插件
    isBlockAttack: false
    # 是否启用分布式事务
    isSeata: false
    # 生产环境请设置p6spy = false
    p6spy: true
    # 单页分页条数限制
    maxLimit: -1
    # 溢出总页数后是否进行处理
    overflow: false
    # 生成 countSql 优化掉 join, 现在只支持 left join
    optimizeJoin: true
    # id生成策略
    id-type: CACHE
    hutoolId:
      workerId: 0
      dataCenterId: 0
    cache-id:
      time-bits: 31
      worker-bits: 22
      seq-bits: 10
      epochStr: '2020-09-15'
      boost-power: 3
      padding-factor: 50

