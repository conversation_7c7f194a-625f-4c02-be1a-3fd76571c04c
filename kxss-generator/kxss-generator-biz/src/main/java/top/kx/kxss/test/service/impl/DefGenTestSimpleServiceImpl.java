package top.kx.kxss.test.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.test.manager.DefGenTestSimpleManager;
import top.kx.kxss.test.service.DefGenTestSimpleService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.test.entity.DefGenTestSimple;
import top.kx.kxss.test.vo.query.DefGenTestSimplePageQuery;
import top.kx.kxss.test.vo.result.DefGenTestSimpleResultVO;
import top.kx.kxss.test.vo.save.DefGenTestSimpleSaveVO;
import top.kx.kxss.test.vo.update.DefGenTestSimpleUpdateVO;

/**
 * <p>
 * 业务实现类
 * 测试单表
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-15 15:36:45
 * @create [2022-04-15 15:36:45] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)

public class DefGenTestSimpleServiceImpl extends SuperServiceImpl<DefGenTestSimpleManager, Long, DefGenTestSimple, DefGenTestSimpleSaveVO,
        DefGenTestSimpleUpdateVO, DefGenTestSimplePageQuery, DefGenTestSimpleResultVO> implements DefGenTestSimpleService {

}


