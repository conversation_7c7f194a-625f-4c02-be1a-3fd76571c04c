package top.kx.kxss.generator.converts;

import top.kx.kxss.generator.config.DateType;
import top.kx.kxss.generator.rules.ColumnType;
import top.kx.kxss.generator.rules.DbColumnType;

import static top.kx.kxss.generator.rules.DbColumnType.BIG_DECIMAL;
import static top.kx.kxss.generator.rules.DbColumnType.BOOLEAN;
import static top.kx.kxss.generator.rules.DbColumnType.BYTE_ARRAY;
import static top.kx.kxss.generator.rules.DbColumnType.DOUBLE;
import static top.kx.kxss.generator.rules.DbColumnType.FLOAT;
import static top.kx.kxss.generator.rules.DbColumnType.INTEGER;
import static top.kx.kxss.generator.rules.DbColumnType.LONG;
import static top.kx.kxss.generator.rules.DbColumnType.STRING;


/**
 * SQLServer 字段类型转换
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/8/12 11:18 AM
 * @create [2022/8/12 11:18 AM ] [tangyh] [初始创建]
 */
public class SqlServerTypeConvert implements ITypeConvert {

    public static final SqlServerTypeConvert INSTANCE = new SqlServerTypeConvert();

    /**
     * 转换为日期类型
     *
     * @param dt   日期类型
     * @param type 类型
     * @return 返回对应的列类型
     */
    public static ColumnType toDateType(DateType dt, String type) {
        switch (dt) {
            case SQL_PACK:
                switch (type) {
                    case "date":
                        return DbColumnType.DATE_SQL;
                    case "time":
                        return DbColumnType.TIME;
                    default:
                        return DbColumnType.TIMESTAMP;
                }
            case TIME_PACK:
                switch (type) {
                    case "date":
                        return DbColumnType.LOCAL_DATE;
                    case "time":
                        return DbColumnType.LOCAL_TIME;
                    default:
                        return DbColumnType.LOCAL_DATE_TIME;
                }
            default:
                return DbColumnType.DATE;
        }
    }

    @Override
    public ColumnType processTypeConvert(DateType datetype, String fieldType, Long size, Integer digit) {
        return TypeConverts.use(fieldType)
                .test(TypeConverts.containsAny("char", "xml", "text").then(STRING))
                .test(TypeConverts.contains("bigint").then(LONG))
                .test(TypeConverts.contains("int").then(INTEGER))
                .test(TypeConverts.containsAny("date", "time").then(t -> toDateType(datetype, t)))
                .test(TypeConverts.contains("bit").then(BOOLEAN))
                .test(TypeConverts.containsAny("decimal", "numeric").then(DOUBLE))
                .test(TypeConverts.contains("money").then(BIG_DECIMAL))
                .test(TypeConverts.containsAny("binary", "image").then(BYTE_ARRAY))
                .test(TypeConverts.containsAny("float", "real").then(FLOAT))
                .or(STRING);
    }
}
