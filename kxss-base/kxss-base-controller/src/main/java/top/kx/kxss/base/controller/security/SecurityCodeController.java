package top.kx.kxss.base.controller.security;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.service.BaseSecurityCodeService;
import top.kx.kxss.base.vo.result.BaseSecurityCodeResultVO;
import top.kx.kxss.base.vo.result.member.RegisterConfirmResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.save.RegisterConfirmSaveVO;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <p>
 * 前端控制器
 * 防伪信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-25 13:40:18
 * @create [2023-05-25 13:40:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/security")
@Api(value = "BaseSecurityCode", tags = "防伪查询及注册-用户端")
public class SecurityCodeController {

    @Resource
    private BaseSecurityCodeService securityCodeService;

    @Resource
    private EchoService echoService;

    @ApiOperation("验证防伪码是否存在")
    @GetMapping("/check")
    @WebLog("验证防伪码是否存在")
    public R<Boolean> check(@RequestParam String code) {
        if (StrUtil.isBlank(code)) {
            return R.success(false);
        }
        return R.success(securityCodeService.check(code));
    }

    @ApiOperation("验证防伪码是否存在")
    @GetMapping("/checkCode")
    @WebLog("验证防伪码是否存在")
    public R<String> checkCode(@RequestParam String code) {
        if (StrUtil.isBlank(code)) {
            return R.success(null);
        }
        return R.success(securityCodeService.checkCode(code));
    }

    @ApiOperation("防伪码密文换取明文")
    @GetMapping("/decrypt")
    @WebLog("防伪码密文换取明文")
    public R<String> decrypt(@RequestParam String code) {
        return R.success(securityCodeService.decrypt(code));
    }


    @ApiOperation("根据防伪码查询绑定商品信息")
    @GetMapping("/product")
    @WebLog("根据防伪码查询绑定商品信息")
    public R<BaseProductResultVO> productBySecurityCode(@RequestParam String securityCode,
                                                        @RequestParam @ApiParam(value = "扫码类型 1 扫码查询 2 NFC查询 3 手动输入") String type,
                                                        @RequestParam(value = "address", required = false) String address,
                                                        @RequestParam(value = "longitude", required = false) BigDecimal longitude,
                                                        @RequestParam(value = "latitude", required = false) BigDecimal latitude) {
        BaseProductResultVO productResultVO = securityCodeService.productBySecurityCode(securityCode, type, address, longitude, latitude);
        echoService.action(productResultVO);
        return R.success(productResultVO);
    }

    @ApiOperation("注册确认页信息")
    @GetMapping("/confirm")
    @WebLog("获取会员信息")
    public R<RegisterConfirmResultVO> confirmInfo(@RequestParam String securityCode) {
        return R.success(securityCodeService.confirmInfo(securityCode));
    }

    @ApiOperation("注册防伪码")
    @PostMapping("/register")
    @WebLog("注册防伪码")
    public R<BaseSecurityCodeResultVO> registerSecurityCode(@RequestBody @Validated RegisterConfirmSaveVO saveVO) {
        return R.success(securityCodeService.registerSecurityCode(saveVO));
    }
}


