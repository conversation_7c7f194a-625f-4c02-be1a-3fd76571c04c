<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>top.kx.kxss</groupId>
        <artifactId>kxss-public</artifactId>
        <version>4.13.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>kxss-file-sdk</artifactId>
    <name>${project.artifactId}</name>
    <description>业务文件sdk</description>

    <dependencies>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-common</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-databases</artifactId>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-mvc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>
