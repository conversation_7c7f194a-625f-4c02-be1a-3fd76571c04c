package top.kx.kxss.test.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.test.entity.DefGenTestSimple;
import top.kx.kxss.test.vo.query.DefGenTestSimplePageQuery;
import top.kx.kxss.test.vo.result.DefGenTestSimpleResultVO;
import top.kx.kxss.test.vo.save.DefGenTestSimpleSaveVO;
import top.kx.kxss.test.vo.update.DefGenTestSimpleUpdateVO;


/**
 * <p>
 * 业务接口
 * 测试单表
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-15 15:36:45
 * @create [2022-04-15 15:36:45] [zuihou] [代码生成器生成]
 */
public interface DefGenTestSimpleService extends SuperService<Long, DefGenTestSimple, DefGenTestSimpleSaveVO,
        DefGenTestSimpleUpdateVO, DefGenTestSimplePageQuery, DefGenTestSimpleResultVO> {

}


