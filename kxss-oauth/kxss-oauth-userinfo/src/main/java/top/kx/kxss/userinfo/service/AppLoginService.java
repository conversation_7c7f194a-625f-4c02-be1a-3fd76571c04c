package top.kx.kxss.userinfo.service;


import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import top.kx.basic.base.R;
import top.kx.kxss.oauth.vo.param.AppAccountPhoneLoginVO;
import top.kx.kxss.oauth.vo.param.AppLoginVO;
import top.kx.kxss.oauth.vo.param.AppPhoneLoginVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;

/**
 * 微信小程序授予token接口
 *
 * <AUTHOR>
 * <AUTHOR>
 * @date 2020年03月31日10:21:21
 */
public interface AppLoginService {


    /**
     * @param entity
     * @return
     */
    WxMaUserInfo getWxInfo(AppLoginVO entity);

    /**
     * 微信手机号登录
     * @param vo
     */
    R<LoginResultVO> wxPhoneLogin(AppPhoneLoginVO vo);


    R<LoginResultVO> empAccountLogin(AppAccountPhoneLoginVO vo);
}
