package top.kx.kxss.userinfo.service.wx.impl;

import cn.binarywang.wx.miniapp.api.WxMaLinkService;
import cn.binarywang.wx.miniapp.api.WxMaSchemeService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.scheme.WxMaGenerateSchemeRequest;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;
import top.kx.kxss.common.properties.WxMaProperties;
import top.kx.kxss.userinfo.config.WxMaConfiguration;
import top.kx.kxss.userinfo.service.wx.WxService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 微信跳转
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxServiceImpl implements WxService {

    @Resource
    private WxMaProperties wxMaProperties;


    @Override
    public String generateScheme(WxMaGenerateSchemeRequest request, String clientId) {
        try {
            //获取客户小程序配置信息
            WxMaProperties.Config wxConfig = wxMaProperties.getConfigs().get(0);
            if (StrUtil.isNotBlank(clientId)) {
                List<WxMaProperties.Config> collect = wxMaProperties.getConfigs().stream().filter(wxConfig1 -> wxConfig1.getClientId()
                        .equals(clientId)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    wxConfig = collect.get(0);
                }
            }
            log.info("参数，{}", request);
            //小程序appid
            String appid = wxConfig.getAppid();
            final WxMaService wxService = WxMaConfiguration.getMaService(appid);
            WxMaSchemeService wxMaSchemeService = wxService.getWxMaSchemeService();
            String generate = wxMaSchemeService.generate(request);
            System.out.println("-------" + generate);
            return generate;
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String generateLink(GenerateUrlLinkRequest request, String clientId) {
        try {
            log.info("参数，{}", request);
            //获取客户小程序配置信息
            WxMaProperties.Config wxConfig = wxMaProperties.getConfigs().get(0);
            if (StrUtil.isNotBlank(clientId)) {
                List<WxMaProperties.Config> collect = wxMaProperties.getConfigs().stream().filter(wxConfig1 -> wxConfig1.getClientId()
                        .equals(clientId)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    wxConfig = collect.get(0);
                }
            }
            //小程序appid
            String appid = wxConfig.getAppid();
            final WxMaService wxService = WxMaConfiguration.getMaService(appid);
            WxMaLinkService linkService = wxService.getLinkService();
            String generate = linkService.generateUrlLink(request);
            System.out.println("-------" + generate);
            return generate;
        } catch (WxErrorException e) {
            throw new RuntimeException(e);
        }
    }
}
