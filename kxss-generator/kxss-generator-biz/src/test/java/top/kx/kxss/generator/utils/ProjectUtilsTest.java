package top.kx.kxss.generator.utils;

import top.kx.basic.database.properties.DatabaseProperties;
import top.kx.kxss.generator.enumeration.ProjectTypeEnum;
import top.kx.kxss.generator.vo.save.ProjectGeneratorVO;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/4/5 5:54 PM
 * @create [2022/4/5 5:54 PM ] [tangyh] [初始创建]
 */
public class ProjectUtilsTest {
    public static void main(String[] args) {
        ProjectGeneratorVO vo = new ProjectGeneratorVO();
        vo.setProjectPrefix("kxss");
        vo.setOutputDir("/Users/<USER>/gitlab/kxss-cloud-pro-datasource-column");
        vo.setType(ProjectTypeEnum.CLOUD);
        vo.setAuthor("阿汤哥");
        vo.setServiceName("test");
        vo.setModuleName("test");
        vo.setParent("top.kx.kxss");
        vo.setGroupId("top.kx.kxss");
        vo.setUtilParent("top.kx.basic");
        vo.setVersion("4.13.1");
        vo.setDescription("测试服务");
        vo.setServerPort(8080);
        ProjectUtils.generator(vo, new DatabaseProperties());
    }
}
