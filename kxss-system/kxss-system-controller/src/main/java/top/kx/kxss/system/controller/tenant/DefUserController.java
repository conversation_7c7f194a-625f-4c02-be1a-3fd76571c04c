package top.kx.kxss.system.controller.tenant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.query.tenant.DefUserPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefUserResultVO;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefUserPasswordResetVO;
import top.kx.kxss.system.vo.update.tenant.DefUserUpdateVO;
import top.kx.kxss.system.vo.update.tenant.UserMobileUpdateVO;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defUser")
@Api(value = "DefUser", tags = "用户")
public class DefUserController extends SuperController<DefUserService, Long, DefUser, DefUserSaveVO, DefUserUpdateVO, DefUserPageQuery, DefUserResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @ApiOperation(value = "检测用户名是否存在")
    @GetMapping("/checkUsername")
    @WebLog("'检测用户名是否存在, username=' + #username + ', id=' + #id")
    public R<Boolean> checkUsername(@RequestParam String username, @RequestParam(required = false) Long id) {
        return success(superService.checkUsername(username, id));
    }

    @ApiOperation(value = "检测邮箱是否存在")
    @GetMapping("/checkEmail")
    @WebLog("'检测邮箱是否存在, email=' + #email + ', id=' + #id")
    public R<Boolean> checkEmail(@RequestParam String email, @RequestParam(required = false) Long id) {
        return success(superService.checkEmail(email, id));
    }

    @ApiOperation(value = "检测身份证是否存在")
    @GetMapping("/checkIdCard")
    @WebLog("'检测身份证是否存在, idCard=' + #idCard + ', id=' + #id")
    public R<Boolean> checkIdCard(@RequestParam String idCard, @RequestParam(required = false) Long id) {
        return success(superService.checkIdCard(idCard, id));
    }

    @ApiOperation(value = "检测手机号是否存在")
    @GetMapping("/checkMobile")
    @WebLog("'检测手机号是否存在, mobile=' + #mobile + ', id=' + #id")
    public R<Boolean> checkMobile(@RequestParam String mobile, @RequestParam(required = false) Long id) {
        return success(superService.checkMobile(mobile, id));
    }

    /**
     * 重置密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "重置密码", notes = "重置密码")
    @PutMapping("/resetPassword")
    @WebLog("'修改密码:' + #data.id")
    public R<Boolean> resetPassword(@RequestBody @Validated DefUserPasswordResetVO data) {
        return success(superService.resetPassword(data));
    }

    /**
     * 重置密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "修改手机号", notes = "修改手机号")
    @PutMapping("/updateMobile")
    @WebLog("'修改手机号:' + #data.id")
    public R<Boolean> updateMobile(@RequestBody @Validated UserMobileUpdateVO data) {
        return success(superService.updateMobile(data));
    }

    /**
     * 修改状态
     *
     * @param id    用户id
     * @param state 用户状态
     * @return 是否成功
     */
    @ApiOperation(value = "修改状态", notes = "修改状态")
    @PutMapping("/updateState")
    @WebLog("'修改状态:id=' + #id + ', state=' + #state")
    public R<Boolean> updateState(
            @NotNull(message = "请选择用户") @RequestParam Long id,
            @NotNull(message = "请设置正确的状态值") @RequestParam Boolean state) {
        return success(superService.updateState(id, state));
    }

    @ApiOperation(value = "查询未绑定到该企业的用户")
    @PostMapping("/findNotUserByTenantId")
    @WebLog("'查询未绑定到该企业的用户")
    public R<IPage<DefUserResultVO>> findNotUserByTenantId(@RequestBody @Validated PageParams<DefUserPageQuery> params) {
        return success(superService.findNotUserByTenantId(params));
    }


    @ApiOperation(value = "查询所有的用户id", notes = "查询所有的用户id")
    @PostMapping(value = "/findAllUserId")
    @WebLog("'查询所有的用户id")
    public R<List<Long>> findAllUserId(@RequestParam Long tenantId) {
        return R.success(superService.findUserIdList(null));
    }

    @ApiOperation(value = "查找同一企业下的用户", notes = "查找同一企业下的用户")
    @PostMapping(value = "/pageUser")
    @WebLog("'查找同一企业下的用户")
    public R<IPage<DefUserResultVO>> pageUser(@RequestBody @Validated PageParams<DefUserPageQuery> params) {
        IPage<DefUserResultVO> page = superService.pageUser(params);
        echoService.action(page);
        return R.success(page);
    }

    @ApiOperation(value = "邀请员工进入企业前精确查询用户", notes = "邀请员工进入企业前精确查询用户")
    @PostMapping(value = "/queryUser")
    @WebLog("'邀请员工进入企业前精确查询用户")
    public R<List<DefUserResultVO>> queryUser(@RequestBody DefUserPageQuery params) {
        return R.success(superService.queryUser(params));
    }
}
