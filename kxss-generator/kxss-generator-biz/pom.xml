<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kxss-generator</artifactId>
        <groupId>top.kx.kxss</groupId>
        <version>4.13.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>kxss-generator-biz</artifactId>
    <name>${project.artifactId}</name>
    <description>在线代码生成器模块-业务模块</description>

    <dependencies>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-generator-entity</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-system-biz</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-base-biz</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-file-sdk</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>

        <dependency>
            <groupId>top.kx.kxss</groupId>
            <artifactId>kxss-data-scope-sdk</artifactId>
            <version>${kxss-project.version}</version>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-all</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>top.kx.kxss</groupId>
                    <artifactId>kxss-cloud-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-echo-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>top.kx.basic</groupId>
            <artifactId>kxss-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
