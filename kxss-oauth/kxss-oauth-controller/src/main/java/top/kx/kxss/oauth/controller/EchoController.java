package top.kx.kxss.oauth.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.annotation.response.IgnoreResponseBodyAdvice;
import top.kx.basic.base.R;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.service.user.BaseOrgService;
import top.kx.kxss.base.service.user.BasePositionService;
import top.kx.kxss.oauth.service.DictService;
import top.kx.kxss.system.service.tenant.DefUserService;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * 数据注入的查询实现类
 *
 * <AUTHOR>
 * @date 2020/9/25 9:15 上午
 */
@Slf4j
@RestController
@AllArgsConstructor()
@RequestMapping("/echo")
@IgnoreResponseBodyAdvice
@Api(value = "数据注入查询接口", tags = "数据注入查询接口， 不建议前端调用")
@ApiIgnore
public class EchoController {
    private final DictService dictService;
    private final BaseOrgService baseOrgService;
    private final DefUserService userService;
    private final BasePositionService basePositionService;
    private final BaseEmployeeService baseEmployeeService;
    private final BaseProductService baseProductService;

    @GetMapping("/anyTenant/test")
    @WebLog
    public R<Object> test(@RequestParam(required = false) Long id) {
        log.info("id={}", id);
        return R.success(id);
    }

    @ApiOperation(value = "根据id查询用户", notes = "根据id查询用户")
    @PostMapping("/user/findByIds")
    public Map<Serializable, Object> findUserByIds(@RequestParam(value = "ids") Set<Serializable> ids) {
        return userService.findByIds(ids);
    }

    @PostMapping("/position/findByIds")
    public Map<Serializable, Object> findStationByIds(@RequestParam("ids") Set<Serializable> ids) {
        return basePositionService.findByIds(ids);
    }

    @PostMapping("/org/findByIds")
    public Map<Serializable, Object> findOrgByIds(@RequestParam("ids") Set<Serializable> ids) {
        return baseOrgService.findByIds(ids);
    }

    @ApiOperation(value = "查询字典项", notes = "根据字典编码查询字典项")
    @PostMapping("/dict/findByIds")
    public Map<Serializable, Object> findDictByIds(@RequestParam("ids") Set<Serializable> ids) {
        return this.dictService.findByIds(ids);
    }

    @PostMapping("/employee/findByIds")
    public Map<Serializable, Object> findEmployeeByIds(@RequestParam("ids") Set<Serializable> ids) {
        return this.baseEmployeeService.findByIds(ids);
    }
    @PostMapping("/product/findByIds")
    public Map<Serializable, Object> findProductByIds(@RequestParam("ids") Set<Serializable> ids) {
        return this.baseProductService.findByIds(ids);
    }

}
