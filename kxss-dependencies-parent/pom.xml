<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>top.kx.basic</groupId>
        <artifactId>kxss-parent</artifactId>
        <version>4.13.1</version>
        <relativePath/>
    </parent>

    <groupId>top.kx.kxss</groupId>
    <artifactId>kxss-dependencies-parent</artifactId>
    <version>4.13.1</version>

    <packaging>pom</packaging>
    <modelVersion>4.0.0</modelVersion>

    <name>${project.artifactId}</name>
    <description>kxss项目业务父pom</description>
    <url>https://github.com/zuihou/kxss-cloud</url>
    <properties>
        <kxss-util.version>4.13.1</kxss-util.version>
        <kxss-project.version>4.13.1</kxss-project.version>
        <docker.image.prefix>dou</docker.image.prefix>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-all</artifactId>
                <version>${kxss-util.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>top.kx.kxss</groupId>
                        <artifactId>kxss-cloud-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-annotation</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-boot</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-cache-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>top.kx.basic</groupId>-->
<!--                <artifactId>kxss-cloud-starter</artifactId>-->
<!--                <version>${kxss-util.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-core</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-databases</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-dozer-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-echo-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-jwt-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-log-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-mq-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-mvc</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-scan-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-swagger2-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-uid</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-validator-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
            <dependency>
                <groupId>top.kx.basic</groupId>
                <artifactId>kxss-xss-starter</artifactId>
                <version>${kxss-util.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
<!--            <scope>provided</scope>-->
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <profile.active>dev</profile.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profile.active>prod</profile.active>
            </properties>
        </profile>
    </profiles>
    <build>
        <finalName>${project.artifactId}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <plugin>
                    <!--打包跳过测试-->
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <configuration>
                        <skipTests>true</skipTests>
                        <skip>true</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <!--打包跳过测试-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>

            <!-- 资源插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>

            </plugin>
            <!--
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            -->
            <!-- 一键更新子模块版本号 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
