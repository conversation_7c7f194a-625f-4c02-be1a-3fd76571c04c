package top.kx.kxss.system.controller.application;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.TreeUtil;
import top.kx.kxss.system.entity.application.DefResource;
import top.kx.kxss.system.service.application.DefResourceService;
import top.kx.kxss.system.vo.query.application.DefResourcePageQuery;
import top.kx.kxss.system.vo.result.application.DefResourceResultVO;
import top.kx.kxss.system.vo.save.application.DefResourceSaveVO;
import top.kx.kxss.system.vo.update.application.DefResourceUpdateVO;

import java.util.List;

import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_STRING;
import static top.kx.kxss.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;


/**
 * <p>
 * 前端控制器
 * 资源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-13
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/defResource")
@Api(value = "DefResource", tags = "资源")
public class DefResourceController extends SuperController<DefResourceService, Long, DefResource, DefResourceSaveVO, DefResourceUpdateVO, DefResourcePageQuery, DefResourceResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "code", value = "编码", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测资源编码是否可用", notes = "检测资源编码是否可用")
    @GetMapping("/check")
    public R<Boolean> check(@RequestParam(required = false) Long id, @RequestParam String code) {
        return success(superService.check(id, code));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "path", value = "编码", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测资源路径是否可用", notes = "检测资源路径是否可用")
    @GetMapping("/checkPath")
    public R<Boolean> checkPath(@RequestParam(required = false) Long id, @RequestParam Long applicationId, @RequestParam String path) {
        return success(superService.checkPath(id, applicationId, path));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "编码", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测资源名称是否可用", notes = "检测资源名称是否可用")
    @GetMapping("/checkName")
    public R<Boolean> checkName(@RequestParam(required = false) Long id, @RequestParam Long applicationId, @RequestParam String name) {
        return success(superService.checkName(id, applicationId, name));
    }

    @Override
    public R<DefResource> handlerSave(DefResourceSaveVO data) {
        return success(superService.saveWithCache(data));
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids) {
        return success(superService.removeByIdWithCache(ids));
    }

    @Override
    public R<DefResource> handlerUpdate(DefResourceUpdateVO data) {
        return success(superService.updateWithCacheById(data));
    }

    /**
     * 查询系统中所有的的资源，按树结构返回
     * 不用缓存，因为该接口很少会使用，就算使用，也会管理员维护菜单时使用
     */
    @ApiOperation(value = "查询系统所有的资源", notes = "查询系统所有的资源")
    @PostMapping("/tree")
    @WebLog("查询系统所有的菜单")
    public R<List<DefResourceResultVO>> allTree(@RequestBody DefResource query) {
        List<DefResource> list = superService.list(Wraps.lbQ(query).orderByAsc(DefResource::getSortValue));
        List<DefResourceResultVO> resultList = BeanPlusUtil.toBeanList(list, DefResourceResultVO.class);
        // 回显 @Echo 标记的字段
        echoService.action(resultList);
        return success(TreeUtil.buildTree(resultList));
    }


    @ApiOperation(value = "移动资源", notes = "移动资源")
    @PutMapping("/moveResource")
    @WebLog("移动资源")
    public R<Boolean> moveResource(@RequestParam Long id, @RequestParam(required = false) Long parentId) {
        superService.moveResource(id, parentId);
        return success();
    }

    @Override
    public R<DefResourceResultVO> get(@PathVariable Long id) {
        return R.success(superService.getResourceById(id));
    }
}
