package top.kx.kxss.test.manager.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.test.manager.DefGenTestSimpleManager;
import top.kx.kxss.test.mapper.DefGenTestSimpleMapper;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.test.entity.DefGenTestSimple;

/**
 * <p>
 * 通用业务实现类
 * 测试单表
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-15 15:36:45
 * @create [2022-04-15 15:36:45] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefGenTestSimpleManagerImpl extends SuperManagerImpl<DefGenTestSimpleMapper, DefGenTestSimple> implements DefGenTestSimpleManager {

}


