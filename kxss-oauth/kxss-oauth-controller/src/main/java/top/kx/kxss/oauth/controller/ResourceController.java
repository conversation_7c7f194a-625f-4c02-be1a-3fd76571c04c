package top.kx.kxss.oauth.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;
import top.kx.basic.annotation.user.LoginUser;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.base.vo.result.user.VueRouter;
import top.kx.kxss.common.properties.IgnoreProperties;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.kxss.oauth.biz.ResourceBiz;
import top.kx.kxss.oauth.vo.result.VisibleResourceVO;

import java.util.Collections;
import java.util.List;

import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static top.kx.kxss.common.constant.SwaggerConstants.DATA_TYPE_STRING;
import static top.kx.kxss.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;


/**
 * <p>
 * 前端控制器
 * 资源 角色 菜单
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-22
 */
@Slf4j
@RestController
@RequestMapping("/anyone")
@AllArgsConstructor
@Api(value = "OauthMenuResource", tags = "资源-菜单-应用")
@EnableConfigurationProperties({IgnoreProperties.class})
public class ResourceController {
    private final IgnoreProperties ignoreProperties;
    private final ResourceBiz oauthResourceBiz;


    /**
     * 查询用户可用的所有资源
     *
     * @param applicationId 应用id
     * @param employeeId    当前登录人id
     */
    @ApiOperation(value = "查询用户可用的所有资源", notes = "查询用户可用的所有资源")
    @GetMapping("/visible/resource")
    public R<VisibleResourceVO> visible(@ApiIgnore @LoginUser SysUser sysUser,
                                        @RequestParam(value = "employeeId", required = false) Long employeeId,
                                        @RequestParam Long applicationId) {
        if (employeeId == null || employeeId <= 0) {
            employeeId = sysUser.getEmployeeId();
        }
        return R.success(VisibleResourceVO.builder()
                .roleList(Collections.singletonList("PT_ADMIN"))
                .resourceList(oauthResourceBiz.findVisibleResource(employeeId, applicationId))
                .caseSensitive(ignoreProperties.getCaseSensitive())
                .enabled(ignoreProperties.getAuthEnabled())
                .build());
    }

    @ApiOperation(value = "查询用户可用的所有资源", notes = "查询用户可用的所有资源")
    @GetMapping("/findVisibleResource")
    public R<List<String>> visibleResource(@RequestParam(value = "employeeId") Long employeeId,
                                           @RequestParam(value = "applicationId", required = false) Long applicationId) {
        return R.success(oauthResourceBiz.findVisibleResource(employeeId, applicationId));
    }

    /**
     * 检查员工是否有指定uri的访问权限
     *
     * @param path   请求路径
     * @param method 请求方法
     */
    @ApiOperation(value = "检查员工是否有指定uri的访问权限", notes = "检查员工是否有指定uri的访问权限")
    @GetMapping("/checkUri")
    public R<Boolean> checkUri(@RequestParam String path, @RequestParam String method) {
        return R.success(oauthResourceBiz.checkUri(path, method));
    }

    /**
     * 检查员工是否有指定应用的权限
     *
     * @param employeeId    员工id
     * @param applicationId 应用ID
     */
    @ApiOperation(value = "检查员工是否有指定应用的权限", notes = "检查员工是否有指定应用的权限")
    @GetMapping("/checkApplication")
    public R<Boolean> checkApplication(@RequestParam Long applicationId, @RequestParam Long employeeId) {
        return R.success(true);
    }

    /**
     * 检测员工是否拥有指定应用的权限
     *
     * @param applicationId 应用id
     */
    @ApiOperation(value = "检测员工是否拥有指定应用的权限", notes = "检测员工是否拥有指定应用的权限")
    @GetMapping("/checkEmployeeHaveApplication")
    public R<Boolean> checkEmployeeHaveApplication(@RequestParam Long applicationId) {
        return R.success(oauthResourceBiz.checkEmployeeHaveApplication(ContextUtil.getEmployeeId(), applicationId));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "subGroup", value = "菜单组", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "employeeId", value = "用户id", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "applicationId", value = "应用id", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "查询用户可用的所有菜单路由树", notes = "查询用户可用的所有菜单路由树")
    @GetMapping("/visible/router")
    public R<List<VueRouter>> myRouter(@RequestParam Long applicationId,
                                       @RequestParam(value = "subGroup", required = false) String subGroup,
                                       @RequestParam(value = "employeeId", required = false) Long employeeId,
                                       @ApiIgnore @LoginUser SysUser sysUser) {
        if (employeeId == null || employeeId <= 0) {
            employeeId = sysUser.getEmployeeId();
        }
        return R.success(oauthResourceBiz.findVisibleRouter(applicationId, employeeId, subGroup));
    }

}
