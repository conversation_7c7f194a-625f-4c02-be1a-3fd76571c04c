package top.kx.kxss.userinfo.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.kxss.userinfo.biz.EmployeeHelperBiz;
import top.kx.basic.base.R;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.kxss.model.vo.result.UserQuery;

/**
 * <AUTHOR>
 * @date 2020年02月24日10:41:49
 */
@Component
public class UserResolverServiceImpl implements UserResolverService {
    @Autowired
    private EmployeeHelperBiz employeeBiz;

    @Override
    public R<SysUser> getById(UserQuery userQuery) {
        return R.success(employeeBiz.getSysUserById(userQuery));
    }
}
