package top.kx.kxss.test.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.test.entity.DefGenTestTree;
import top.kx.kxss.test.vo.query.DefGenTestTreePageQuery;
import top.kx.kxss.test.vo.result.DefGenTestTreeResultVO;
import top.kx.kxss.test.vo.save.DefGenTestTreeSaveVO;
import top.kx.kxss.test.vo.update.DefGenTestTreeUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 测试树结构
 * </p>
 *
 * <AUTHOR>
 * @date 2022-04-20 00:28:30
 * @create [2022-04-20 00:28:30] [zuihou] [代码生成器生成]
 */
public interface DefGenTestTreeService extends SuperService<Long, DefGenTestTree, DefGenTestTreeSaveVO,
        DefGenTestTreeUpdateVO, DefGenTestTreePageQuery, DefGenTestTreeResultVO> {

    /**
     * 查询树结构
     *
     * @param query 参数
     * @return 树
     */
    List<DefGenTestTree> findTree(DefGenTestTreePageQuery query);
}


